import React, { useState, useEffect, memo } from "react";
import { Box, Typography } from "@mui/material";
import NumberInput from "../../ui/NumberInput";
import ColorPicker from "../../ui/ColorPicker";
import useProjectStore from "../../../store/Store";

interface BaseInfoGroupProps {
  selectedObject: any;
  forceUpdate: () => void;
}

const BaseInfoGroup: React.FC<BaseInfoGroupProps> = memo(({ selectedObject, forceUpdate }) => {
  // 获取所有选中的对象和Store实例
  const selectedObjectsState = useProjectStore(state => state.selectedObjects);
  const setSelectedObjectsProperty = useProjectStore(state => state.setSelectedObjectsProperty);

  // 获取选中的对象数组 - 提取真实对象
  const selectedObjects = selectedObjectsState.objects.map(obj => obj.object);
  // 使用 useState 在组件内部管理属性值
  const [xValue, setXValue] = useState(selectedObject?.x || 0);
  const [yValue, setYValue] = useState(selectedObject?.y || 0);
  const [widthValue, setWidthValue] = useState(selectedObject?.width || 0);
  const [heightValue, setHeightValue] = useState(selectedObject?.height || 0);
  const [anchorXValue, setAnchorXValue] = useState(selectedObject?.anchor?.x || 0);
  const [anchorYValue, setAnchorYValue] = useState(selectedObject?.anchor?.y || 0);
  const [colorValue, setColorValue] = useState(selectedObject?.tint || "#FFFFFF");
  const [hasBorder, setHasBorder] = useState(!!selectedObject?.border);
  const [borderWidthValue, setBorderWidthValue] = useState(selectedObject?.border?.width || 0);
  const [borderColorValue, setBorderColorValue] = useState(selectedObject?.border?.color || "#000000");

  // 新增属性
  const [rotationValue, setRotationValue] = useState(selectedObject?.rotation || 0);
  const [opacityValue, setOpacityValue] = useState(
    selectedObject?.alpha !== undefined ? selectedObject.alpha : 1
  );
  const [skewXValue, setSkewXValue] = useState(selectedObject?.skew?.x || 0);
  const [skewYValue, setSkewYValue] = useState(selectedObject?.skew?.y || 0);

  // 当选中对象变化时，更新本地状态
  useEffect(() => {
    if (selectedObject) {
      // 位置属性
      setXValue(selectedObject.x || 0);
      setYValue(selectedObject.y || 0);

      // 尺寸属性
      setWidthValue(selectedObject.width || 0);
      setHeightValue(selectedObject.height || 0);

      // 锚点属性
      setAnchorXValue(selectedObject.anchor?.x || 0);
      setAnchorYValue(selectedObject.anchor?.y || 0);

      // 颜色属性 - 检查不同的颜色属性名
      if (selectedObject.tint) {
        setColorValue(selectedObject.tint);
      } else if (selectedObject.color) {
        setColorValue(selectedObject.color);
      } else if (selectedObject._color) {
        setColorValue(selectedObject._color);
      } else {
        setColorValue("#FFFFFF");
      }

      // 边框属性
      const hasBorderProperty = !!selectedObject.border ||
        !!selectedObject.borderWidth ||
        !!selectedObject.borderColor;
      setHasBorder(hasBorderProperty);

      if (hasBorderProperty) {
        if (selectedObject.border) {
          setBorderWidthValue(selectedObject.border.width || 0);
          setBorderColorValue(selectedObject.border.color || "#000000");
        } else {
          setBorderWidthValue(selectedObject.borderWidth || 0);
          setBorderColorValue(selectedObject.borderColor || "#000000");
        }
      } else {
        setBorderWidthValue(0);
        setBorderColorValue("#000000");
      }

      // 新增属性
      // 旋转属性
      setRotationValue(selectedObject.rotation || 0);

      // 透明度属性
      setOpacityValue(selectedObject.alpha !== undefined ? selectedObject.alpha : 1);

      // 倾斜属性
      setSkewXValue(selectedObject.skew?.x || 0);
      setSkewYValue(selectedObject.skew?.y || 0);
    }
  }, [selectedObject]);

  // 处理属性滑动过程中的值更新（不记录历史）
  const handlePropertyMove = (propertyPath: string, value: any) => {
    console.log(`属性滑动中: ${propertyPath} = ${value}`);

    try {
      // 修改对象属性
      if (selectedObjects.length > 0) {
        // 1. 更新本地状态
        switch (propertyPath) {
          case "x":
            setXValue(value);
            break;
          case "y":
            setYValue(value);
            break;
          case "width":
            setWidthValue(value);
            break;
          case "height":
            setHeightValue(value);
            break;
          case "anchor.x":
            setAnchorXValue(value);
            break;
          case "anchor.y":
            setAnchorYValue(value);
            break;
          case "border.width":
            setBorderWidthValue(value);
            break;
          case "rotation":
            setRotationValue(value);
            break;
          case "alpha":
            setOpacityValue(value);
            break;
          case "skew.x":
            setSkewXValue(value);
            break;
          case "skew.y":
            setSkewYValue(value);
            break;
          default:
            console.log(`未处理的属性滑动: ${propertyPath}`);
            return;
        }

        // 2. 使用Store中的统一方法更新属性
        setSelectedObjectsProperty(propertyPath, value);
      }
    } catch (error) {
      console.error("处理属性滑动时出错:", error);
    }
  };

  // 处理属性值变化
  const handlePropertyChange = async (propertyPath: string, value: any, providedOldValue?: any) => {
    console.log(`属性值变化: ${propertyPath} = ${value}, 提供的旧值: ${providedOldValue}`);

    try {
      // 修改对象属性
      if (selectedObjects.length > 0) {
        console.log("BaseInfoGroup - 修改对象属性:", selectedObject.constructor?.name, propertyPath, value);

        // 1. 更新本地状态
        switch (propertyPath) {
          case "x":
            setXValue(value);
            break;
          case "y":
            setYValue(value);
            break;
          case "width":
            setWidthValue(value);
            break;
          case "height":
            setHeightValue(value);
            break;
          case "anchor.x":
            setAnchorXValue(value);
            break;
          case "anchor.y":
            setAnchorYValue(value);
            break;
          case "tint":
          case "color":
            setColorValue(value);
            break;
          case "border.width":
            setBorderWidthValue(value);
            break;
          case "border.color":
            setBorderColorValue(value);
            break;
          case "rotation":
            setRotationValue(value);
            break;
          case "alpha":
            setOpacityValue(value);
            break;
          case "skew.x":
            setSkewXValue(value);
            break;
          case "skew.y":
            setSkewYValue(value);
            break;
          default:
            console.log(`未处理的属性变化: ${propertyPath}`);
            return;
        }

        // 2. 使用Store中的统一方法更新属性
        setSelectedObjectsProperty(propertyPath, value);
        console.log(`属性 ${propertyPath} 已通过Store更新为 ${value}`);
      }
    } catch (error) {
      console.error("处理属性变化时出错:", error);
    }
  };

  return (
    <Box>
      {/* 对象名称居中显示 */}
      <Box sx={{ textAlign: 'center', mb: 0, borderBottom: '1px solid #eee', pb: 0 }}>
        <Typography sx={{ fontWeight: 'bold' }}>
          {selectedObject.constructor.name}
          {selectedObjectsState.selectionType === "multiple" && (
            <span style={{ marginLeft: '8px', fontSize: '0.8em', color: '#666' }}>
              (已选择 {selectedObjects.length} 个对象)
            </span>
          )}
        </Typography>
      </Box>

      {/* 位置部分 - 紧凑的横向布局 */}
      <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
        <Typography variant="body2" sx={{ fontWeight: 'bold', minWidth: '40px' }}>
          位置:
        </Typography>

        {/* X坐标 */}
        <Box sx={{ display: "flex", alignItems: "center", mr: 1, flex: 1, pl: 0 }}>
          <Typography variant="body2" sx={{ mr: 0.2, fontSize: '0.85rem', whiteSpace: 'nowrap' }}>
            X:
          </Typography>
          <Box sx={{ flex: 1, ml: 0, pl: 0 }}>
            <NumberInput
              label=""
              value={xValue}
              onChange={(newValue, oldValue) => handlePropertyChange("x", newValue, oldValue)}
              onMove={(newValue) => handlePropertyMove("x", newValue)}
              width="100%"
              object={selectedObject[0]}
              propertyName="x"
            />
          </Box>
        </Box>

        {/* Y坐标 */}
        <Box sx={{ display: "flex", alignItems: "center", flex: 1 }}>
          <Typography variant="body2" sx={{ mr: 0.2, fontSize: '0.85rem', whiteSpace: 'nowrap' }}>
            Y:
          </Typography>
          <Box sx={{ flex: 1, ml: 0, pl: 0 }}>
            <NumberInput
              label=""
              value={yValue}
              onChange={(newValue, oldValue) => handlePropertyChange("y", newValue, oldValue)}
              onMove={(newValue) => handlePropertyMove("y", newValue)}
              width="100%"
              object={selectedObject[0]}
              propertyName="y"
            />
          </Box>
        </Box>
      </Box>

      {/* 尺寸部分 - 紧凑的横向布局 */}
      <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
        <Typography variant="body2" sx={{ fontWeight: 'bold', minWidth: '40px' }}>
          尺寸:
        </Typography>

        {/* 宽度 */}
        <Box sx={{ display: "flex", alignItems: "center", mr: 1, flex: 1, pl: 0 }}>
          <Typography variant="body2" sx={{ mr: 0.2, fontSize: '0.85rem', whiteSpace: 'nowrap' }}>
            宽:
          </Typography>
          <Box sx={{ flex: 1, ml: 0, pl: 0 }}>
            <NumberInput
              label=""
              value={widthValue}
              onChange={(newValue, oldValue) => handlePropertyChange("width", newValue, oldValue)}
              onMove={(newValue) => handlePropertyMove("width", newValue)}
              width="100%"
              object={selectedObject[0]}
              propertyName="width"
            />
          </Box>
        </Box>

        {/* 高度 */}
        <Box sx={{ display: "flex", alignItems: "center", flex: 1 }}>
          <Typography variant="body2" sx={{ mr: 0.2, fontSize: '0.85rem', whiteSpace: 'nowrap' }}>
            高:
          </Typography>
          <Box sx={{ flex: 1, ml: 0, pl: 0 }}>
            <NumberInput
              label=""
              value={heightValue}
              onChange={(newValue, oldValue) => handlePropertyChange("height", newValue, oldValue)}
              onMove={(newValue) => handlePropertyMove("height", newValue)}
              width="100%"
              object={selectedObject[0]}
              propertyName="height"
            />
          </Box>
        </Box>
      </Box>

      {/* 锚点部分 - 紧凑的横向布局 */}
      <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
        <Typography variant="body2" sx={{ fontWeight: 'bold', minWidth: '40px' }}>
          锚点:
        </Typography>

        {/* 锚点X */}
        <Box sx={{ display: "flex", alignItems: "center", mr: 1, flex: 1, pl: 0 }}>
          <Typography variant="body2" sx={{ mr: 0.2, fontSize: '0.85rem', whiteSpace: 'nowrap' }}>
            X:
          </Typography>
          <Box sx={{ flex: 1, ml: 0, pl: 0 }}>
            <NumberInput
              label=""
              value={anchorXValue}
              onChange={(newValue, oldValue) => handlePropertyChange("anchor.x", newValue, oldValue)}
              onMove={(newValue) => handlePropertyMove("anchor.x", newValue)}
              width="100%"
              min={0}
              max={1}
              step={0.1}
              precision={2}
              object={selectedObject[0]}
              propertyName="anchor.x"
            />
          </Box>
        </Box>

        {/* 锚点Y */}
        <Box sx={{ display: "flex", alignItems: "center", flex: 1 }}>
          <Typography variant="body2" sx={{ mr: 0.2, fontSize: '0.85rem', whiteSpace: 'nowrap' }}>
            Y:
          </Typography>
          <Box sx={{ flex: 1, ml: 0, pl: 0 }}>
            <NumberInput
              label=""
              value={anchorYValue}
              onChange={(newValue, oldValue) => handlePropertyChange("anchor.y", newValue, oldValue)}
              onMove={(newValue) => handlePropertyMove("anchor.y", newValue)}
              width="100%"
              min={0}
              max={1}
              step={0.1}
              precision={2}
              object={selectedObject[0]}
              propertyName="anchor.y"
            />
          </Box>
        </Box>
      </Box>

      {/* 变换部分 - 新增 */}
      <Box sx={{ mb: 1 }}>
        <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 0.5 }}>
          变换:
        </Typography>

        {/* 旋转 */}
        <Box sx={{ display: "flex", alignItems: "center", mb: 0.5 }}>
          <Typography variant="body2" sx={{ mr: 1, fontSize: '0.85rem', whiteSpace: 'nowrap', width: '60px' }}>
            旋转:
          </Typography>
          <Box sx={{ flex: 1 }}>
            <NumberInput
              label=""
              value={rotationValue}
              onChange={(newValue, oldValue) => handlePropertyChange("rotation", newValue, oldValue)}
              onMove={(newValue) => handlePropertyMove("rotation", newValue)}
              width="100%"
              min={0}
              max={Math.PI * 2}
              step={0.1}
              precision={2}
              object={selectedObject[0]}
              propertyName="rotation"
            />
          </Box>
        </Box>

        {/* 倾斜X */}
        <Box sx={{ display: "flex", alignItems: "center", mb: 0.5 }}>
          <Typography variant="body2" sx={{ mr: 1, fontSize: '0.85rem', whiteSpace: 'nowrap', width: '60px' }}>
            倾斜X:
          </Typography>
          <Box sx={{ flex: 1 }}>
            <NumberInput
              label=""
              value={skewXValue}
              onChange={(newValue, oldValue) => handlePropertyChange("skew.x", newValue, oldValue)}
              onMove={(newValue) => handlePropertyMove("skew.x", newValue)}
              width="100%"
              min={-2}
              max={2}
              step={0.1}
              precision={2}
              object={selectedObject[0]}
              propertyName="skew.x"
            />
          </Box>
        </Box>

        {/* 倾斜Y */}
        <Box sx={{ display: "flex", alignItems: "center", mb: 0.5 }}>
          <Typography variant="body2" sx={{ mr: 1, fontSize: '0.85rem', whiteSpace: 'nowrap', width: '60px' }}>
            倾斜Y:
          </Typography>
          <Box sx={{ flex: 1 }}>
            <NumberInput
              label=""
              value={skewYValue}
              onChange={(newValue, oldValue) => handlePropertyChange("skew.y", newValue, oldValue)}
              onMove={(newValue) => handlePropertyMove("skew.y", newValue)}
              width="100%"
              min={-2}
              max={2}
              step={0.1}
              precision={2}
              object={selectedObject[0]}
              propertyName="skew.y"
            />
          </Box>
        </Box>

        {/* 透明度 */}
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Typography variant="body2" sx={{ mr: 1, fontSize: '0.85rem', whiteSpace: 'nowrap', width: '60px' }}>
            透明度:
          </Typography>
          <Box sx={{ flex: 1 }}>
            <NumberInput
              label=""
              value={opacityValue}
              onChange={(newValue, oldValue) => handlePropertyChange("alpha", newValue, oldValue)}
              onMove={(newValue) => handlePropertyMove("alpha", newValue)}
              width="100%"
              min={0}
              max={1}
              step={0.1}
              precision={2}
              object={selectedObject[0]}
              propertyName="alpha"
            />
          </Box>
        </Box>
      </Box>

      {/* 颜色部分 */}
      <Box sx={{ mb: 1 }}>
        <ColorPicker
          label="颜色"
          value={colorValue}
          onChange={(value) => handlePropertyChange("tint", value)}
          width="100%"
          compact={true}
        />
      </Box>

      {/* 边框部分 - 如果对象有边框属性 */}
      {hasBorder && (
        <Box sx={{ mb: 1 }}>
          <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 0.5 }}>
            边框:
          </Typography>

          {/* 边框宽度 */}
          <Box sx={{ display: "flex", alignItems: "center", mb: 0.5 }}>
            <Typography variant="body2" sx={{ mr: 1, fontSize: '0.85rem', whiteSpace: 'nowrap', width: '60px' }}>
              宽度:
            </Typography>
            <Box sx={{ flex: 1 }}>
              <NumberInput
                label=""
                value={borderWidthValue}
                onChange={(newValue, oldValue) => handlePropertyChange("border.width", newValue, oldValue)}
                onMove={(newValue) => handlePropertyMove("border.width", newValue)}
                width="100%"
                min={0}
                max={10}
                step={1}
                precision={0}
                object={selectedObject[0]}
                propertyName="border.width"
              />
            </Box>
          </Box>

          {/* 边框颜色 */}
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Typography variant="body2" sx={{ mr: 1, fontSize: '0.85rem', whiteSpace: 'nowrap', width: '60px' }}>
              颜色:
            </Typography>
            <Box sx={{ flex: 1 }}>
              <ColorPicker
                value={borderColorValue}
                onChange={(value) => handlePropertyChange("border.color", value)}
                width="100%"
                compact={true}
              />
            </Box>
          </Box>
        </Box>
      )}
    </Box>
  );
}, (prevProps, nextProps) => {
  // 只有当selectedObject发生变化时才重新渲染
  return prevProps.selectedObject === nextProps.selectedObject;
});

export default BaseInfoGroup;