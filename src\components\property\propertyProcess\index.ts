/**
 * 属性处理器统一入口
 * 根据属性类型自动选择合适的处理器
 */

import { BaseInfoProcessor, BaseInfoProperties } from './BaseInfoProcessor';
import { ImageInfoProcessor, ImageInfoProperties } from './ImageInfoProcessor';
import { SpriteColorProcessor, SpriteColorProperties } from './SpriteColorProcessor';
import { TextInfoProcessor, TextInfoProperties } from './TextInfoProcessor';
import { FilterProcessor, FilterProperties } from './FilterProcessor';

// 导出所有处理器
export {
  BaseInfoProcessor,
  ImageInfoProcessor,
  SpriteColorProcessor,
  TextInfoProcessor,
  FilterProcessor
};

// 导出所有属性接口
export type {
  BaseInfoProperties,
  ImageInfoProperties,
  SpriteColorProperties,
  TextInfoProperties,
  FilterProperties
};

// 统一的属性类型
export type AllProperties = BaseInfoProperties & 
                           ImageInfoProperties & 
                           SpriteColorProperties & 
                           TextInfoProperties & 
                           FilterProperties;

/**
 * 统一的属性处理器
 * 自动根据属性名选择合适的处理器
 */
export class PropertyProcessor {
  /**
   * 应用单个属性到对象
   */
  static applyProperty(
    object: any,
    propertyName: keyof AllProperties,
    value: any
  ): void {
    if (!object) {
      console.warn('PropertyProcessor: 对象为空');
      return;
    }

    try {
      // 根据属性名选择合适的处理器
      const processor = this.getProcessorForProperty(propertyName);
      
      if (processor) {
        processor.applyProperty(object, propertyName as any, value);
      } else {
        console.warn(`PropertyProcessor: 未找到属性 ${propertyName} 的处理器`);
      }
    } catch (error) {
      console.error(`PropertyProcessor: 应用属性 ${propertyName} 失败:`, error);
    }
  }

  /**
   * 批量应用属性到对象
   */
  static applyProperties(
    object: any,
    properties: Partial<AllProperties>
  ): void {
    if (!object) {
      console.warn('PropertyProcessor: 对象为空');
      return;
    }

    // 按处理器类型分组属性
    const groupedProperties = this.groupPropertiesByProcessor(properties);

    // 分别应用每组属性
    for (const [processor, props] of groupedProperties) {
      try {
        if (processor && typeof processor.applyProperties === 'function') {
          processor.applyProperties(object, props);
        } else {
          // 如果处理器没有批量方法，逐个应用
          for (const [propName, value] of Object.entries(props)) {
            if (value !== undefined && value !== null) {
              this.applyProperty(object, propName as keyof AllProperties, value);
            }
          }
        }
      } catch (error) {
        console.error('PropertyProcessor: 批量应用属性失败:', error);
      }
    }
  }

  /**
   * 根据属性名获取对应的处理器
   */
  private static getProcessorForProperty(propertyName: string): any {
    // 基础信息属性
    const baseInfoProps = ['x', 'y', 'width', 'height', 'scaleX', 'scaleY', 'rotation', 'alpha', 'visible', 'zIndex', 'name'];
    if (baseInfoProps.includes(propertyName)) {
      return BaseInfoProcessor;
    }

    // 图片信息属性
    const imageInfoProps = ['texture', 'bitmap._url', 'anchor', 'tint', 'blendMode', 'frame'];
    if (imageInfoProps.includes(propertyName)) {
      return ImageInfoProcessor;
    }

    // Sprite颜色属性
    const spriteColorProps = ['hue', 'colorTone', 'blendColor'];
    if (spriteColorProps.includes(propertyName)) {
      return SpriteColorProcessor;
    }

    // 文字信息属性
    const textInfoProps = ['text', 'fontSize', 'fontFace', 'fontBold', 'fontItalic', 'textColor', 'outlineColor', 'outlineWidth', 'textAlign', 'textBaseline', 'wordWrap', 'wordWrapWidth', 'lineHeight'];
    if (textInfoProps.includes(propertyName)) {
      return TextInfoProcessor;
    }

    // 滤镜属性
    const filterProps = ['filters', 'filters.add', 'filters.remove', 'filters.modify', 'filters.reorder'];
    if (filterProps.includes(propertyName) || propertyName.startsWith('filters.')) {
      return FilterProcessor;
    }

    return null;
  }

  /**
   * 按处理器类型分组属性
   */
  private static groupPropertiesByProcessor(
    properties: Partial<AllProperties>
  ): Map<any, any> {
    const groups = new Map();

    for (const [propName, value] of Object.entries(properties)) {
      if (value !== undefined && value !== null) {
        const processor = this.getProcessorForProperty(propName);
        
        if (processor) {
          if (!groups.has(processor)) {
            groups.set(processor, {});
          }
          groups.get(processor)[propName] = value;
        }
      }
    }

    return groups;
  }

  /**
   * 验证属性值
   */
  static validateProperty(
    propertyName: keyof AllProperties,
    value: any
  ): boolean {
    try {
      const processor = this.getProcessorForProperty(propertyName);
      
      if (processor && typeof processor.validateProperty === 'function') {
        return processor.validateProperty(propertyName, value);
      }
      
      // 如果没有专门的验证器，返回true
      return true;
    } catch (error) {
      console.error(`PropertyProcessor: 验证属性 ${propertyName} 失败:`, error);
      return false;
    }
  }

  /**
   * 获取属性的默认值
   */
  static getDefaultValue(propertyName: keyof AllProperties): any {
    try {
      const processor = this.getProcessorForProperty(propertyName);
      
      if (processor && typeof processor.getDefaultValue === 'function') {
        return processor.getDefaultValue(propertyName);
      }
      
      return null;
    } catch (error) {
      console.error(`PropertyProcessor: 获取属性 ${propertyName} 默认值失败:`, error);
      return null;
    }
  }

  /**
   * 检查对象是否支持某个属性
   */
  static isPropertySupported(object: any, propertyName: keyof AllProperties): boolean {
    if (!object) return false;

    try {
      // 检查对象是否有该属性
      if (propertyName in object) {
        return true;
      }

      // 检查是否是特殊属性（如嵌套属性）
      if (propertyName.includes('.')) {
        const parts = propertyName.split('.');
        let current = object;
        
        for (const part of parts) {
          if (current && part in current) {
            current = current[part];
          } else {
            return false;
          }
        }
        
        return true;
      }

      return false;
    } catch (error) {
      console.error(`PropertyProcessor: 检查属性支持失败 (${propertyName}):`, error);
      return false;
    }
  }

  /**
   * 获取对象的所有可用属性
   */
  static getAvailableProperties(object: any): string[] {
    if (!object) return [];

    const availableProps: string[] = [];

    // 检查基础信息属性
    const baseInfoProps = ['x', 'y', 'width', 'height', 'scaleX', 'scaleY', 'rotation', 'alpha', 'visible', 'zIndex', 'name'];
    baseInfoProps.forEach(prop => {
      if (this.isPropertySupported(object, prop as keyof AllProperties)) {
        availableProps.push(prop);
      }
    });

    // 检查图片信息属性
    const imageInfoProps = ['texture', 'bitmap._url', 'anchor', 'tint', 'blendMode', 'frame'];
    imageInfoProps.forEach(prop => {
      if (this.isPropertySupported(object, prop as keyof AllProperties)) {
        availableProps.push(prop);
      }
    });

    // 检查Sprite颜色属性
    const spriteColorProps = ['hue', 'colorTone', 'blendColor'];
    spriteColorProps.forEach(prop => {
      if (this.isPropertySupported(object, prop as keyof AllProperties)) {
        availableProps.push(prop);
      }
    });

    // 检查文字信息属性
    const textInfoProps = ['text', 'fontSize', 'fontFace', 'fontBold', 'fontItalic', 'textColor', 'outlineColor', 'outlineWidth', 'textAlign', 'textBaseline', 'wordWrap', 'wordWrapWidth', 'lineHeight'];
    textInfoProps.forEach(prop => {
      if (this.isPropertySupported(object, prop as keyof AllProperties)) {
        availableProps.push(prop);
      }
    });

    // 检查滤镜属性
    if (object.filters !== undefined) {
      availableProps.push('filters');
    }

    return availableProps;
  }
}
