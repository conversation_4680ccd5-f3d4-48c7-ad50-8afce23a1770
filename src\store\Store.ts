import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { DisplayObject } from 'pixi.js';
import { PropertyProcessor } from '../components/property/propertyProcess';
import { getObjectPath } from '../utils/object/objectPro';
import { globalOperationManager } from '../generators';
import { PropertyUpdate } from '../utils/common/debounce';

// 批量处理属性更新的函数
async function processBatchPropertyUpdates(updates: PropertyUpdate[]): Promise<void> {
  const results: Array<{ success: boolean; error?: any }> = [];

  try {
    // 批量应用属性更改
    for (const update of updates) {
      try {
        // 1. 使用 PropertyProcessor 处理属性
        PropertyProcessor.applyProperty(
          update.object,
          update.fieldName as any,
          update.newValue
        );

        // 2. 记录到 generators 系统
        globalOperationManager.setProperty(
          update.objectPath,
          update.className,
          update.fieldName,
          update.newValue
        );

        results.push({ success: true });
        console.log(`成功处理属性更新: ${update.className}[${update.objectPath.join('/')}].${update.fieldName} = ${update.newValue}`);

      } catch (error) {
        results.push({ success: false, error });
        console.error(`处理属性更新失败: ${update.className}[${update.objectPath.join('/')}].${update.fieldName}`, error);
      }
    }

    // 检查是否有失败的操作
    const failedCount = results.filter(r => !r.success).length;
    if (failedCount > 0) {
      console.warn(`批量属性更新完成，但有 ${failedCount} 个操作失败`);
    }

  } catch (error) {
    console.error('批量属性更新过程中发生严重错误:', error);
    throw error;
  }
}

// 操作类型
export enum OperationType {
  MODIFY_PROPERTY = 'ModifyProperty',
  ADD_OBJECT = 'AddObject',
  DELETE_OBJECT = 'DeleteObject',
}

// 操作详情接口
export interface OperationDetails {
  propertyName?: string;
  oldValue?: any;
  newValue?: any;
  // 可以根据需要添加更多字段
}

// 操作记录接口
export interface Operation {
  id: string;
  operation_type: OperationType;
  timestamp: number;
  object_path: string[];
  details: OperationDetails;
}

// 定义事件回调类型
type EventCallback = (...args: any[]) => void;

// 资源类型枚举
export enum AssetType {
  Image = 'Image',
  Audio = 'Audio',
  Data = 'Data',
  Map = 'Map',
  Font = 'Font',
  Video = 'Video',
  Other = 'Other',
  Directory = 'Directory',
}

// 资源信息接口
export interface AssetInfo {
  name: string;                // 资源名称
  asset_type: AssetType;       // 资源类型
  game_path: string;           // 游戏项目中的相对路径
  absolute_path: string;       // 电脑中的绝对路径
  display_path: string;        // Tauri 中显示的路径
  is_directory: boolean;       // 是否是目录
  children?: AssetInfo[];      // 子资源（如果是目录）
  parent_path?: string;        // 父目录路径
  size?: number;               // 文件大小（字节）
  extension?: string;          // 文件扩展名
}

// 项目信息接口
export interface Project {
  name: string;
  path: string;
}



// 定义项目状态接口
interface ProjectState {
  // 项目信息
  projectName: string;
  projectPath: string;
  projectSelected: boolean;
  currentProject: Project | null;
  setProjectName: (name: string) => void;
  setProjectPath: (path: string) => void;
  setProjectSelected: (selected: boolean) => void;
  setCurrentProject: (project: Project | null) => void;
  // 切换场景时
  scene: any
  setScene: (scene: any) => void;

  // 游戏窗口引用
  gameWindow: any;
  setGameWindow: (window: any) => void;

  //历史记录
  history: Operation[];
  addToHistory: (operation: Operation) => void;
  clearHistory: () => void;

  // 选中对象相关
  selectedObjects: {
    type: "object" | "class" | "none"; // 选中的是对象还是类型
    selectionType: "single" | "multiple" | "none"; // 单选还是多选
    objects: Array<{
      object: DisplayObject;
      path: string[]; // 缓存的对象路径
      className: string; // 缓存的类名
    }>; // 选中的对象数组（带缓存信息）
    className?: string; // 如果选中的是类型，则存储类型名称
  };
  setSelectedObjects: (objects: DisplayObject[], type?: "object" | "class", className?: string) => void;
  addSelectedObject: (object: DisplayObject, type?: "object" | "class") => void;
  clearSelectedObjects: () => void;
  setSelectedObjectsProperty: (fieldName: string, newValue: any) => void; // 更新选中对象属性并刷新UI

  // 事件系统相关方法

  // 资源相关
  assets: AssetInfo[];
  currentAssetPath: string;
  selectedAsset: AssetInfo | null;
  loadingAssets: boolean;
  setAssets: (assets: AssetInfo[]) => void;
  setCurrentAssetPath: (path: string) => void;
  setSelectedAsset: (asset: AssetInfo | null) => void;
  setLoadingAssets: (loading: boolean) => void;

  // 事件系统
  events: Map<string, EventCallback[]>;
  emit: (eventName: string, ...args: any[]) => void;
  on: (eventName: string, callback: EventCallback) => void;
  off: (eventName: string, callback: EventCallback) => void;

  // UI更新相关
  lastPropertyUpdate?: number; // 最后一次属性更新的时间戳
}

// 创建 Zustand store
const useProjectStore = create<ProjectState>()(
  persist(
    (set, get) => ({
      // 项目信息
      projectName: '',
      projectPath: '',
      projectSelected: false,
      currentProject: null,
      setProjectName: (name) => set({
        projectName: name,
        currentProject: name ? { name, path: get().projectPath } : null
      }),
      setProjectPath: (path) => set({
        projectPath: path,
        currentProject: path ? { name: get().projectName, path } : null
      }),
      setProjectSelected: (selected) => set({ projectSelected: selected }),
      setCurrentProject: (project) => set({ currentProject: project }),
      // 切换场景时，
      scene: null,
      setScene: (scene) => set({ scene }),

      // 游戏窗口引用
      gameWindow: window,
      setGameWindow: (gameWindow) => set({ gameWindow }),

      // 选中对象相关
      selectedObjects: {
        type: "none",
        selectionType: "none",
        objects: [],
        className: undefined
      },
      setSelectedObjects: (objects, type = "object", className) => set((state) => {
        // 为每个对象创建带缓存信息的结构
        const objectsWithCache = objects.map(obj => ({
          object: obj,
          path: getObjectPath(obj),
          className: obj.constructor?.name || 'Unknown'
        }));

        return {
          ...state,
          selectedObjects: {
            type: objects.length > 0 ? type : "none",
            selectionType: objects.length > 1 ? "multiple" : objects.length === 1 ? "single" : "none",
            objects: objectsWithCache,
            className: className
          }
        };
      }),
      addSelectedObject: (object, type = "object") => set((state) => {
        // 检查对象是否已经在选中列表中
        const isAlreadySelected = state.selectedObjects.objects.some(obj => obj.object === object);
        if (isAlreadySelected) return state;

        // 创建带缓存信息的对象结构
        const objectWithCache = {
          object: object,
          path: getObjectPath(object),
          className: object.constructor?.name || 'Unknown'
        };

        // 添加对象到选中列表
        const newObjects = [...state.selectedObjects.objects, objectWithCache];
        return {
          ...state,
          selectedObjects: {
            type: type,
            selectionType: newObjects.length > 1 ? "multiple" : "single",
            objects: newObjects,
            className: state.selectedObjects.className
          }
        };
      }),
      clearSelectedObjects: () => set((state) => ({
        ...state,
        selectedObjects: {
          type: "none",
          selectionType: "none",
          objects: [],
          className: undefined
        }
      })),

      setSelectedObjectsProperty: async (fieldName: string, newValue: any) => {
        console.log(`Store - 更新属性: ${fieldName} = ${newValue}`);

        const state = get();
        const updates: PropertyUpdate[] = [];

        try {
          // 为每个选中的对象创建更新记录
          for (const objInfo of state.selectedObjects.objects) {
            const { object, path, className } = objInfo;

            // 获取旧值用于操作记录
            const oldValue = (object as any)[fieldName];

            // 创建更新记录
            updates.push({
              object,
              objectPath: path,
              className,
              fieldName,
              newValue,
              oldValue
            });
          }

          // 批量处理属性更新
          await processBatchPropertyUpdates(updates);

          console.log(`属性 ${fieldName} 更新成功，影响对象数量: ${updates.length}`);

          // 触发UI更新通知
          set(state => ({
            ...state,
            lastPropertyUpdate: Date.now()
          }));

          // 触发事件通知
          get().emit('propertyUpdated', { fieldName, newValue, affectedCount: updates.length });

        } catch (error) {
          console.error('批量属性更新失败:', error);

          // 触发错误事件
          get().emit('propertyUpdateError', { fieldName, newValue, error });
        }
      },

      // 历史记录相关方法

      // 历史记录
      history: [],

      // 添加操作到历史记录
      addToHistory: (operation) => {
        console.log('Store - 添加历史记录:', operation);
        set((state) => ({
          history: [...state.history, operation]
        }));
      },

      // 清空历史记录
      clearHistory: () => {
        console.log('Store - 清空历史记录');
        set({ history: [] });
      },

      // 资源相关
      assets: [],
      currentAssetPath: '',
      selectedAsset: null,
      loadingAssets: false,
      setAssets: (assets) => set({ assets }),
      setCurrentAssetPath: (path) => set({ currentAssetPath: path }),
      setSelectedAsset: (asset) => set({ selectedAsset: asset }),
      setLoadingAssets: (loading) => set({ loadingAssets: loading }),

      // 事件系统
      events: new Map(),

      emit: (eventName, ...args) => {
        console.log(`Store - 触发事件: ${eventName}`, args);
        const callbacks = get().events.get(eventName) || [];
        callbacks.forEach((callback) => callback(...args));
      },

      on: (eventName, callback) => {
        console.log(`Store - 注册事件监听器: ${eventName}`);
        const state = get();
        const callbacks = state.events.get(eventName) || [];
        state.events.set(eventName, [...callbacks, callback]);
      },

      off: (eventName, callback) => {
        console.log(`Store - 移除事件监听器: ${eventName}`);
        const state = get();
        const callbacks = state.events.get(eventName) || [];
        state.events.set(
          eventName,
          callbacks.filter((cb) => cb !== callback)
        );
      }
    }),
    {
      name: 'rpg-editor-storage', // 存储的名称
      partialize: (state) => ({
        projectName: state.projectName,
        projectPath: state.projectPath,
        projectSelected: state.projectSelected
      }), // 只持久化项目信息
    }
  )
);

export default useProjectStore;