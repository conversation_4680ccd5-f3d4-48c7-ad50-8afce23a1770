/**
 * 统一的后端服务
 * 整合所有与后端交互的API
 * 先调用OperationManager中的方法，再调用后端API
 */

import { invoke } from '@tauri-apps/api/core';
import { globalOperationManager } from '../generators';

export interface SaveCodeRequest {
  code: string;
  sceneName?: string;
  filename?: string;
}

export interface SaveCodeResponse {
  success: boolean;
  message: string;
  filePath?: string;
}

export interface RefreshPreviewRequest {
  force?: boolean;
}

export interface RefreshPreviewResponse {
  success: boolean;
  message: string;
}

/**
 * 统一的后端服务类
 */
export class BackendService {

  /**
   * 保存代码到后端
   * 如果没有提供代码，则自动生成统一代码
   */
  static async saveCode(request: SaveCodeRequest): Promise<SaveCodeResponse> {
    try {
      // 如果没有提供代码，则从OperationManager生成
      let codeToSave = request.code;
      if (!codeToSave || codeToSave.trim() === '') {
        codeToSave = globalOperationManager.generateUnifiedCode();
        console.log('自动生成代码，长度:', codeToSave.length);
      }

      const response = await invoke<SaveCodeResponse>('save_operations_record', {
        code: codeToSave,
        sceneName: request.sceneName || 'default',
        filename: request.filename || 'generated_modifications.js'
      });

      console.log('代码保存成功:', response);
      return response;
    } catch (error) {
      console.error('保存代码失败:', error);
      return {
        success: false,
        message: `保存失败: ${error}`
      };
    }
  }

  /**
   * 刷新预览
   */
  static async refreshPreview(request: RefreshPreviewRequest = {}): Promise<RefreshPreviewResponse> {
    try {
      const response = await invoke<RefreshPreviewResponse>('refresh_preview', {
        force: request.force || false
      });

      console.log('预览刷新成功:', response);
      return response;
    } catch (error) {
      console.error('刷新预览失败:', error);
      return {
        success: false,
        message: `刷新失败: ${error}`
      };
    }
  }

  /**
   * 保存操作记录到文件
   * 如果没有提供操作数据，则自动序列化OperationManager的数据
   */
  static async saveOperationRecord(
    projectName: string,
    operationData?: string
  ): Promise<SaveCodeResponse> {
    try {
      // 如果没有提供操作数据，则从OperationManager序列化
      let dataToSave = operationData;
      if (!dataToSave) {
        dataToSave = JSON.stringify(globalOperationManager.toJSON(), null, 2);
        console.log('自动序列化操作数据，操作数量:', globalOperationManager.size());
      }

      const response = await invoke<SaveCodeResponse>('save_operation_record', {
        projectName,
        operationData: dataToSave
      });

      console.log('操作记录保存成功:', response);
      return response;
    } catch (error) {
      console.error('保存操作记录失败:', error);
      return {
        success: false,
        message: `保存操作记录失败: ${error}`
      };
    }
  }

  /**
   * 加载操作记录从文件
   * 自动加载到OperationManager中
   */
  static async loadOperationRecord(projectName: string): Promise<boolean> {
    try {
      const response = await invoke<string>('load_operation_record', {
        projectName
      });

      if (response) {
        // 解析并加载到OperationManager
        const operationData = JSON.parse(response);
        globalOperationManager.fromJSON(operationData);
        console.log('操作记录加载成功，操作数量:', globalOperationManager.size());
        return true;
      } else {
        console.log('没有找到操作记录文件');
        return false;
      }
    } catch (error) {
      console.error('加载操作记录失败:', error);
      return false;
    }
  }

  /**
   * 清空操作记录
   * 同时清空OperationManager中的数据
   */
  static async clearOperationRecord(projectName: string): Promise<SaveCodeResponse> {
    try {
      // 先清空OperationManager
      globalOperationManager.clear();
      console.log('已清空OperationManager中的操作');

      const response = await invoke<SaveCodeResponse>('clear_operation_record', {
        projectName
      });

      console.log('操作记录清空成功:', response);
      return response;
    } catch (error) {
      console.error('清空操作记录失败:', error);
      return {
        success: false,
        message: `清空操作记录失败: ${error}`
      };
    }
  }

  /**
   * 获取项目信息
   */
  static async getProjectInfo(): Promise<any> {
    try {
      const response = await invoke('get_project_info');
      return response;
    } catch (error) {
      console.error('获取项目信息失败:', error);
      return null;
    }
  }

  /**
   * 保存并刷新（组合操作）
   * 如果没有提供代码，则自动生成
   */
  static async saveAndRefresh(
    code?: string,
    sceneName?: string,
    filename?: string
  ): Promise<{ saveResult: SaveCodeResponse; refreshResult: RefreshPreviewResponse }> {
    // 先保存代码（如果没有提供代码，saveCode会自动生成）
    const saveResult = await this.saveCode({
      code: code || '',
      sceneName,
      filename
    });

    // 如果保存成功，则刷新预览
    let refreshResult: RefreshPreviewResponse;
    if (saveResult.success) {
      refreshResult = await this.refreshPreview({ force: true });
    } else {
      refreshResult = {
        success: false,
        message: '由于保存失败，跳过刷新'
      };
    }

    return {
      saveResult,
      refreshResult
    };
  }

  /**
   * 批量保存多个场景的代码
   * 如果没有提供场景代码映射，则自动从OperationManager生成
   */
  static async saveMultipleScenes(
    sceneCodeMap?: Map<string, string>
  ): Promise<Map<string, SaveCodeResponse>> {
    const results = new Map<string, SaveCodeResponse>();

    // 如果没有提供场景代码映射，则从OperationManager生成
    let codeMap = sceneCodeMap;
    if (!codeMap || codeMap.size === 0) {
      codeMap = globalOperationManager.generateCodeByScene();
      console.log('自动生成场景代码，场景数量:', codeMap.size);
    }

    for (const [sceneName, code] of codeMap) {
      const result = await this.saveCode({
        code,
        sceneName,
        filename: `${sceneName}_modifications.js`
      });

      results.set(sceneName, result);
    }

    return results;
  }

  /**
   * 检查后端连接状态
   */
  static async checkConnection(): Promise<boolean> {
    try {
      await invoke('ping');
      return true;
    } catch (error) {
      console.error('后端连接检查失败:', error);
      return false;
    }
  }

  /**
   * 获取后端版本信息
   */
  static async getVersion(): Promise<string | null> {
    try {
      const version = await invoke<string>('get_version');
      return version;
    } catch (error) {
      console.error('获取版本信息失败:', error);
      return null;
    }
  }
}

// 导出便捷方法
export const {
  saveCode,
  refreshPreview,
  saveOperationRecord,
  loadOperationRecord,
  clearOperationRecord,
  getProjectInfo,
  saveAndRefresh,
  saveMultipleScenes,
  checkConnection,
  getVersion
} = BackendService;
