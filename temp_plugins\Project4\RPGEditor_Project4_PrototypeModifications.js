(function() {
"use strict";
// ==================== RPG Editor 生成的修改代码 ====================
// 此代码由 RPG Editor 自动生成，请勿手动修改
// 生成时间: 2025/5/29 14:05:08



// ==================== 对象查找方法 ====================
    // 获取插件参数
    const parameters = PluginManager.parameters("RPGEditor_PrototypeModifications");
    const DEBUG = parameters["debug"] === "true";

    // 调试日志函数
    function log(message, ...args) {
        if (DEBUG) {
            console.log("[RPGEditor]", message, ...args);
        }
    }
    // 工具函数：根据场景路径查找对象
    function findObjectByScenePath(scenePath) {
        if (!scenePath || scenePath.length === 0) return null;

        let current = SceneManager._scene;
        if (!current) return null;

        // 检查第一个元素是否为创建操作标记
        const firstElement = scenePath[0];
        let isCreationOperation = firstElement === "+";

        // 确定实际的场景路径起始位置
        let startIndex = isCreationOperation ? 1 : 0;

        // 验证场景名称
        const expectedSceneName = scenePath[startIndex];
        const actualSceneName = current.constructor.name;
        if (actualSceneName !== expectedSceneName) {
            if (DEBUG) console.log(`[Scene Mismatch] Expected: ${expectedSceneName}, Actual: ${actualSceneName}`);
            return null;
        }

        // 确定遍历的结束位置
        let endIndex = scenePath.length;
        if (isCreationOperation) {
            // 创建操作：最后一个索引不查找，那是要创建的位置
            endIndex = scenePath.length - 1;
            if (DEBUG) console.log(`[Creation Operation] Finding parent object, skipping last index: ${scenePath[scenePath.length - 1]}`);
        }

        // 遍历路径
        for (let i = startIndex + 1; i < endIndex; i++) {
            const index = parseInt(scenePath[i]);
            if (isNaN(index) || !current.children || !current.children[index]) {
                if (DEBUG) console.log(`[Path Break] Index ${index} does not exist in ${current.constructor.name}`);
                return null;
            }
            current = current.children[index];
        }

        return current;
    }


    // 工具函数：创建游戏对象
    function createGameObject(type, params = {}) {
        if (DEBUG) log(`[GameObject] Creating new object: ${type}`, params);

        switch (type) {
            case "Sprite":
                const sprite = new Sprite();
                sprite.name = params.name || "NewSprite";
                sprite.x = params.x || 0;
                sprite.y = params.y || 0;
                sprite.visible = params.visible !== undefined ? params.visible : true;
                return sprite;

            case "Label":
                const label = new Sprite();
                label.name = params.name || "NewLabel";
                label.x = params.x || 0;
                label.y = params.y || 0;
                label.visible = params.visible !== undefined ? params.visible : true;

                const bitmap = new Bitmap(200, 40);
                bitmap.fontSize = 20;
                bitmap.textColor = "#ffffff";
                bitmap.outlineColor = "rgba(0, 0, 0, 0.5)";
                bitmap.outlineWidth = 4;
                bitmap.drawText(params.text || "New Text", 0, 0, 200, 40, "left");
                bitmap.text = params.text || "New Text";

                label.bitmap = bitmap;
                return label;

            case "Container":
                const container = new PIXI.Container();
                container.name = params.name || "NewContainer";
                container.x = params.x || 0;
                container.y = params.y || 0;
                container.visible = params.visible !== undefined ? params.visible : true;
                return container;

            case "Window":
                const rect = new Rectangle(0, 0, 200, 100);
                const windowObj = new Window_Base(rect);
                windowObj.name = params.name || "NewWindow";
                windowObj.x = params.x || 0;
                windowObj.y = params.y || 0;
                windowObj.visible = params.visible !== undefined ? params.visible : true;
                return windowObj;

            case "Button":
                const button = new Sprite_Clickable();
                button.name = params.name || "NewButton";
                button.x = params.x || 0;
                button.y = params.y || 0;
                button.visible = params.visible !== undefined ? params.visible : true;

                const buttonBitmap = new Bitmap(120, 40);
                buttonBitmap.fillRect(0, 0, 120, 40, "#3498db");
                buttonBitmap.fontSize = 18;
                buttonBitmap.textColor = "#ffffff";
                buttonBitmap.drawText(params.text || "Button", 0, 0, 120, 40, "center");

                button.bitmap = buttonBitmap;
                button._isButton = true;
                return button;

            case "LayoutContainer":
                if (typeof LayoutContainer !== "undefined") {
                    const layoutContainer = new LayoutContainer();
                    layoutContainer.name = params.name || "NewLayoutContainer";
                    layoutContainer.x = params.x || 0;
                    layoutContainer.y = params.y || 0;
                    layoutContainer.visible = params.visible !== undefined ? params.visible : true;
                    return layoutContainer;
                } else {
                    // 如果没有LayoutContainer类，创建一个普通的Container
                    const fallbackContainer = new PIXI.Container();
                    fallbackContainer.name = params.name || "NewLayoutContainer";
                    fallbackContainer.x = params.x || 0;
                    fallbackContainer.y = params.y || 0;
                    fallbackContainer.visible = params.visible !== undefined ? params.visible : true;
                    return fallbackContainer;
                }

            default:
                if (DEBUG) log(`[GameObject] Unknown object type: ${type}`);
                return null;
        }
    }
// ==================== Scene_Menu 场景修改 ====================

// 修改 Scene_Menu 场景
const originalScene_MenuStart = Scene_Menu.prototype.start;
Scene_Menu.prototype.start = function() {
    // 调用原始方法
    originalScene_MenuStart.apply(this, arguments);

    if (DEBUG) console.log('Scene_Menu.start 被调用');

    // 设置 Sprite 的 1 个属性
    const target_sprite_Scene_Menu_1_3_1_2Path = ["Scene_Menu","1","3","1","2"];
    const target_sprite_Scene_Menu_1_3_1_2 = findObjectByScenePath(target_sprite_Scene_Menu_1_3_1_2Path);
    if (target_sprite_Scene_Menu_1_3_1_2) {
        // 设置包含图片的 elements 数组
        target_sprite_Scene_Menu_1_3_1_2._bitmap.elements = [{"type":"text","text":"里德","x":77,"y":9,"maxWidth":168,"lineHeight":36,"align":"left","bounds":{"x":184,"y":13,"width":168,"height":36}},{"type":"text","text":"等级","x":184,"y":49,"maxWidth":48,"lineHeight":36,"align":"left","bounds":{"x":184,"y":49,"width":48,"height":36}},{"type":"text","text":1,"x":268,"y":49,"maxWidth":36,"lineHeight":36,"align":"right","bounds":{"x":268,"y":49,"width":36,"height":36}},{"type":"text","text":"剑客","x":364,"y":13,"maxWidth":168,"lineHeight":36,"align":"left","bounds":{"x":364,"y":13,"width":168,"height":36}},{"type":"image","source":{"_url":"../projects/Project4/img/faces/Actor1.png","width":576,"height":288},"sx":432,"sy":7,"sw":144,"sh":129,"dx":257,"dy":53,"dw":144,"dh":129,"bounds":{"x":5,"y":138,"width":144,"height":129}},{"type":"text","text":"米歇尔","x":184,"y":148,"maxWidth":168,"lineHeight":36,"align":"left","bounds":{"x":184,"y":148,"width":168,"height":36}},{"type":"text","text":"等级","x":184,"y":184,"maxWidth":48,"lineHeight":36,"align":"left","bounds":{"x":184,"y":184,"width":48,"height":36}},{"type":"text","text":1,"x":268,"y":184,"maxWidth":36,"lineHeight":36,"align":"right","bounds":{"x":268,"y":184,"width":36,"height":36}},{"type":"text","text":"武术家","x":364,"y":148,"maxWidth":168,"lineHeight":36,"align":"left","bounds":{"x":364,"y":148,"width":168,"height":36}},{"type":"image","source":{"_url":"../projects/Project4/img/faces/Actor1.png","width":576,"height":288},"sx":144,"sy":151,"sw":144,"sh":129,"dx":5,"dy":273,"dw":144,"dh":129,"bounds":{"x":5,"y":273,"width":144,"height":129}},{"type":"text","text":"凯西","x":184,"y":283,"maxWidth":168,"lineHeight":36,"align":"left","bounds":{"x":184,"y":283,"width":168,"height":36}},{"type":"text","text":"等级","x":184,"y":319,"maxWidth":48,"lineHeight":36,"align":"left","bounds":{"x":184,"y":319,"width":48,"height":36}},{"type":"text","text":1,"x":268,"y":319,"maxWidth":36,"lineHeight":36,"align":"right","bounds":{"x":268,"y":319,"width":36,"height":36}},{"type":"text","text":"巫师","x":364,"y":283,"maxWidth":168,"lineHeight":36,"align":"left","bounds":{"x":364,"y":283,"width":168,"height":36}},{"type":"image","source":{"_url":"../projects/Project4/img/faces/Actor1.png","width":576,"height":288},"sx":288,"sy":151,"sw":144,"sh":129,"dx":5,"dy":408,"dw":144,"dh":129,"bounds":{"x":5,"y":408,"width":144,"height":129}},{"type":"text","text":"埃利奥特","x":184,"y":418,"maxWidth":168,"lineHeight":36,"align":"left","bounds":{"x":184,"y":418,"width":168,"height":36}},{"type":"text","text":"等级","x":184,"y":454,"maxWidth":48,"lineHeight":36,"align":"left","bounds":{"x":184,"y":454,"width":48,"height":36}},{"type":"text","text":1,"x":268,"y":454,"maxWidth":36,"lineHeight":36,"align":"right","bounds":{"x":268,"y":454,"width":36,"height":36}},{"type":"text","text":"祭司","x":364,"y":418,"maxWidth":168,"lineHeight":36,"align":"left","bounds":{"x":364,"y":418,"width":168,"height":36}},{"type":"text","text":"里德","x":11,"y":8,"maxWidth":168,"lineHeight":36,"align":"left","bounds":{"x":184,"y":13,"width":168,"height":36}},{"type":"text","text":"等级","x":178,"y":11,"maxWidth":48,"lineHeight":36,"align":"left","bounds":{"x":184,"y":49,"width":48,"height":36}},{"type":"text","text":"剑客","x":364,"y":13,"maxWidth":168,"lineHeight":36,"align":"left","bounds":{"x":364,"y":13,"width":168,"height":36}},{"type":"text","text":"米歇尔","x":184,"y":148,"maxWidth":168,"lineHeight":36,"align":"left","bounds":{"x":184,"y":148,"width":168,"height":36}},{"type":"text","text":"等级","x":184,"y":184,"maxWidth":48,"lineHeight":36,"align":"left","bounds":{"x":184,"y":184,"width":48,"height":36}},{"type":"text","text":"武术家","x":364,"y":148,"maxWidth":168,"lineHeight":36,"align":"left","bounds":{"x":364,"y":148,"width":168,"height":36}},{"type":"text","text":"凯西","x":184,"y":283,"maxWidth":168,"lineHeight":36,"align":"left","bounds":{"x":184,"y":283,"width":168,"height":36}},{"type":"text","text":"等级","x":184,"y":319,"maxWidth":48,"lineHeight":36,"align":"left","bounds":{"x":184,"y":319,"width":48,"height":36}},{"type":"text","text":"巫师","x":364,"y":283,"maxWidth":168,"lineHeight":36,"align":"left","bounds":{"x":364,"y":283,"width":168,"height":36}},{"type":"text","text":"埃利奥特","x":184,"y":418,"maxWidth":168,"lineHeight":36,"align":"left","bounds":{"x":184,"y":418,"width":168,"height":36}},{"type":"text","text":"等级","x":184,"y":454,"maxWidth":48,"lineHeight":36,"align":"left","bounds":{"x":184,"y":454,"width":48,"height":36}},{"type":"text","text":"祭司","x":364,"y":418,"maxWidth":168,"lineHeight":36,"align":"left","bounds":{"x":364,"y":418,"width":168,"height":36}},{"type":"image","source":{"_url":"../projects/Project4/img/enemies/Actor1_3.png","width":224,"height":228},"sx":0,"sy":0,"sw":224,"sh":228,"dx":-55,"dy":38,"dw":224,"dh":228,"bounds":{"x":160,"y":223,"width":224,"height":228}}];
        // 为元素 4 加载图片
        const imagePath_4 = "../projects/Project4/img/faces/Actor1.png";
        const imageBitmap_4 = ImageManager.loadBitmapFromUrl(imagePath_4);
        imageBitmap_4.addLoadListener(function (bitmap) {
            // 设置元素 4 的source为bitmap对象
            if (target_sprite_Scene_Menu_1_3_1_2._bitmap.elements[4]) {
                target_sprite_Scene_Menu_1_3_1_2._bitmap.elements[4].source = bitmap;
                target_sprite_Scene_Menu_1_3_1_2._bitmap.redrawing();
                if (DEBUG) console.log('图片元素 4 加载完成:', imagePath_4);
            }
        });
        // 为元素 9 加载图片
        const imagePath_9 = "../projects/Project4/img/faces/Actor1.png";
        const imageBitmap_9 = ImageManager.loadBitmapFromUrl(imagePath_9);
        imageBitmap_9.addLoadListener(function (bitmap) {
            // 设置元素 9 的source为bitmap对象
            if (target_sprite_Scene_Menu_1_3_1_2._bitmap.elements[9]) {
                target_sprite_Scene_Menu_1_3_1_2._bitmap.elements[9].source = bitmap;
                target_sprite_Scene_Menu_1_3_1_2._bitmap.redrawing();
                if (DEBUG) console.log('图片元素 9 加载完成:', imagePath_9);
            }
        });
        // 为元素 14 加载图片
        const imagePath_14 = "../projects/Project4/img/faces/Actor1.png";
        const imageBitmap_14 = ImageManager.loadBitmapFromUrl(imagePath_14);
        imageBitmap_14.addLoadListener(function (bitmap) {
            // 设置元素 14 的source为bitmap对象
            if (target_sprite_Scene_Menu_1_3_1_2._bitmap.elements[14]) {
                target_sprite_Scene_Menu_1_3_1_2._bitmap.elements[14].source = bitmap;
                target_sprite_Scene_Menu_1_3_1_2._bitmap.redrawing();
                if (DEBUG) console.log('图片元素 14 加载完成:', imagePath_14);
            }
        });
        // 为元素 31 加载图片
        const imagePath_31 = "../projects/Project4/img/enemies/Actor1_3.png";
        const imageBitmap_31 = ImageManager.loadBitmapFromUrl(imagePath_31);
        imageBitmap_31.addLoadListener(function (bitmap) {
            // 设置元素 31 的source为bitmap对象
            if (target_sprite_Scene_Menu_1_3_1_2._bitmap.elements[31]) {
                target_sprite_Scene_Menu_1_3_1_2._bitmap.elements[31].source = bitmap;
                target_sprite_Scene_Menu_1_3_1_2._bitmap.redrawing();
                if (DEBUG) console.log('图片元素 31 加载完成:', imagePath_31);
            }
        });
        if (DEBUG) console.log('设置 _bitmap.elements 数组，长度:', target_sprite_Scene_Menu_1_3_1_2._bitmap.elements.length);
    }

};

// ==================== 代码生成完成 ====================
})();