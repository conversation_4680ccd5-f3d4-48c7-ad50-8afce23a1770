D:\gpgmake\rpgEditor\src-tauri\target\debug\deps\librpgeditor-a0b7b9f1ae9fb079.rmeta: src\main.rs src\asset_manager.rs src\coreRPGtype\mod.rs src\coreRPGtype\readJs.rs src\coreRPGtype\type_reader.rs src\file_utils.rs src\history.rs src\project.rs src\save\mod.rs src\save\api\mod.rs src\save\api\entry.rs src\save\path_utils.rs src\save\plugin_parser.rs src\save\plugins_file.rs src\save\save.rs src\save\types.rs src\temp_plugins.rs src\utils.rs D:\gpgmake\rpgEditor\src-tauri\target\debug\build\rpgeditor-ce48af5f825a7332\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

D:\gpgmake\rpgEditor\src-tauri\target\debug\deps\rpgeditor-a0b7b9f1ae9fb079.d: src\main.rs src\asset_manager.rs src\coreRPGtype\mod.rs src\coreRPGtype\readJs.rs src\coreRPGtype\type_reader.rs src\file_utils.rs src\history.rs src\project.rs src\save\mod.rs src\save\api\mod.rs src\save\api\entry.rs src\save\path_utils.rs src\save\plugin_parser.rs src\save\plugins_file.rs src\save\save.rs src\save\types.rs src\temp_plugins.rs src\utils.rs D:\gpgmake\rpgEditor\src-tauri\target\debug\build\rpgeditor-ce48af5f825a7332\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

src\main.rs:
src\asset_manager.rs:
src\coreRPGtype\mod.rs:
src\coreRPGtype\readJs.rs:
src\coreRPGtype\type_reader.rs:
src\file_utils.rs:
src\history.rs:
src\project.rs:
src\save\mod.rs:
src\save\api\mod.rs:
src\save\api\entry.rs:
src\save\path_utils.rs:
src\save\plugin_parser.rs:
src\save\plugins_file.rs:
src\save\save.rs:
src\save\types.rs:
src\temp_plugins.rs:
src\utils.rs:
D:\gpgmake\rpgEditor\src-tauri\target\debug\build\rpgeditor-ce48af5f825a7332\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=rpgeditor
# env-dep:OUT_DIR=D:\\gpgmake\\rpgEditor\\src-tauri\\target\\debug\\build\\rpgeditor-ce48af5f825a7332\\out
