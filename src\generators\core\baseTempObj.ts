
/**
 * BaseTempObj - 代表一个修改的对象
 * 包含对象的生成代码面板，支持序列化
 * 只有字段的值不为null说明发生了变化，就需要生成这个字段的修改代码
 */

export class BaseTempObj {
  private objectPath: string[];
  private className: string;
  private operationType: 'modify' | 'create' | 'delete';
  private modifiedProperties: Map<string, any> = new Map();

  constructor(objectPath: string[], className: string, operationType: 'modify' | 'create' | 'delete' = 'modify') {
    this.objectPath = objectPath;
    this.className = className;
    this.operationType = operationType;
  }

  /**
   * 从JSON格式的BaseTempObj创建一个BaseTempObj对象
   */
  static fromJSON(jsonData: any): BaseTempObj {
    const obj = new BaseTempObj(jsonData.objectPath, jsonData.className, jsonData.operationType);

    // 重建modifiedProperties Map
    if (jsonData.modifiedProperties) {
      for (const [key, value] of Object.entries(jsonData.modifiedProperties)) {
        if (value !== null && value !== undefined) {
          obj.modifiedProperties.set(key, value);
        }
      }
    }

    return obj;
  }

  /**
   * 序列化为JSON
   */
  toJSON(): any {
    const result: any = {
      objectPath: this.objectPath,
      className: this.className,
      operationType: this.operationType,
      modifiedProperties: {}
    };

    // 将Map转换为普通对象
    for (const [key, value] of this.modifiedProperties) {
      result.modifiedProperties[key] = value;
    }

    return result;
  }

  /**
   * 设置属性值
   */
  setProperty(propertyName: string, value: any): void {
    try {
      // 对于复杂对象，避免序列化到日志
      let logValue = value;
      if (this.isComplexObject(value)) {
        logValue = this.getSimpleDescription(value);
      }

      console.log(`BaseTempObj: 设置属性 ${this.className}[${this.objectPath.join('/')}].${propertyName} = ${logValue}`);

      // 如果值不为null，添加到修改属性Map中
      if (value !== null && value !== undefined) {
        this.modifiedProperties.set(propertyName, value);
        console.log(`BaseTempObj: 属性已添加到修改列表，当前修改属性数量: ${this.modifiedProperties.size}`);
      } else {
        // 如果值为null，从修改属性Map中移除
        this.modifiedProperties.delete(propertyName);
        console.log(`BaseTempObj: 属性已从修改列表移除，当前修改属性数量: ${this.modifiedProperties.size}`);
      }
    } catch (error) {
      console.error(`BaseTempObj: 设置属性失败 ${this.className}[${this.objectPath.join('/')}].${propertyName}:`, error);
      throw error;
    }
  }

  /**
   * 检查是否为复杂对象（包含循环引用）
   */
  private isComplexObject(value: any): boolean {
    if (value === null || value === undefined) return false;
    if (typeof value !== 'object') return false;

    // 检查是否为数组
    if (Array.isArray(value)) {
      // 检查数组中是否有复杂对象
      return value.some(item => this.isComplexObject(item));
    }

    // 检查是否为 PIXI 对象或其他复杂对象
    if (value.constructor && (
      value.constructor.name.includes('Filter') ||
      value.constructor.name.includes('Texture') ||
      value.constructor.name.includes('BaseTexture') ||
      value.constructor.name.includes('RenderTexture') ||
      value.constructor.name === 'Events' ||
      value.constructor.name === 'EE'
    )) {
      return true;
    }

    // 检查是否有循环引用的常见属性
    if (value._events || value.baseTexture || value.context) {
      return true;
    }

    return false;
  }

  /**
   * 获取复杂对象的简单描述
   */
  private getSimpleDescription(value: any): string {
    if (value === null || value === undefined) return String(value);

    if (Array.isArray(value)) {
      if (value.length === 0) return '[]';

      // 如果是滤镜数组
      if (value[0] && value[0].constructor && value[0].constructor.name.includes('Filter')) {
        return `[${value.length} filters: ${value.map(f => f.constructor?.name || 'Unknown').join(', ')}]`;
      }

      // 其他数组
      return `[Array(${value.length})]`;
    }

    if (typeof value === 'object') {
      const className = value.constructor?.name || 'Object';
      return `[${className}]`;
    }

    return String(value);
  }

  /**
   * 获取属性值
   */
  getProperty(propertyName: string): any {
    return this.modifiedProperties.get(propertyName);
  }

  /**
   * 获取所有已修改的属性（使用Map记录）
   */
  getModifiedProperties(): Array<{ name: string; value: any }> {
    const modified: Array<{ name: string; value: any }> = [];

    for (const [propertyName, value] of this.modifiedProperties) {
      if (value !== null && value !== undefined) {
        modified.push({ name: propertyName, value });
      }
    }

    return modified;
  }

  /**
   * 获取修改属性的名称集合
   */
  getModifiedPropertyNames(): Set<string> {
    return new Set(this.modifiedProperties.keys());
  }

  /**
   * 检查属性是否已修改
   */
  isPropertyModified(propertyName: string): boolean {
    return this.modifiedProperties.has(propertyName);
  }

  /**
   * 清除属性修改记录
   */
  clearPropertyModification(propertyName: string): void {
    this.modifiedProperties.delete(propertyName);
  }

  /**
   * 清除所有修改记录
   */
  clearAllModifications(): void {
    this.modifiedProperties.clear();
  }

  /**
   * 生成对象变量名
   */
  private generateVariableName(): string {
    const pathStr = this.objectPath.join('_');
    const classPrefix = this.className.toLowerCase();
    return `target_${classPrefix}_${pathStr}`;
  }

  /**
   * 生成对象查找代码
   */
  private generateObjectLookupCode(): string[] {
    const variableName = this.generateVariableName();
    const pathArrayStr = JSON.stringify(this.objectPath);

    return [
      `const ${variableName}Path = ${pathArrayStr};`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`
    ];
  }

  /**
   * 生成基础属性代码
   */
  private generateBasicPropertyCode(propertyName: string, value: any): string[] {
    const variableName = this.generateVariableName();
    const lines: string[] = [];

    // 特殊处理滤镜属性
    if (propertyName === 'filters') {
      return this.generateFiltersPropertyCode(value);
    }

    if (typeof value === 'string') {
      lines.push(`    ${variableName}.${propertyName} = "${value}";`);
    } else if (typeof value === 'number' || typeof value === 'boolean') {
      lines.push(`    ${variableName}.${propertyName} = ${value};`);
    } else if (Array.isArray(value)) {
      // 检查是否为复杂对象数组
      if (this.isComplexObject(value)) {
        lines.push(`    // 跳过复杂对象数组 ${propertyName} (包含循环引用)`);
        lines.push(`    // ${this.getSimpleDescription(value)}`);
      } else {
        lines.push(`    ${variableName}.${propertyName} = ${JSON.stringify(value)};`);
      }
    } else if (this.isComplexObject(value)) {
      lines.push(`    // 跳过复杂对象 ${propertyName} (包含循环引用)`);
      lines.push(`    // ${this.getSimpleDescription(value)}`);
    } else {
      try {
        lines.push(`    ${variableName}.${propertyName} = ${JSON.stringify(value)};`);
      } catch (error: any) {
        lines.push(`    // 跳过无法序列化的属性 ${propertyName}`);
        lines.push(`    // Error: ${error?.message || 'Unknown error'}`);
      }
    }

    // 安全的日志输出
    try {
      if (this.isComplexObject(value)) {
        lines.push(`    if (DEBUG) console.log('设置属性:', '${propertyName}', '${this.getSimpleDescription(value)}', ${variableName});`);
      } else {
        lines.push(`    if (DEBUG) console.log('设置属性:', '${propertyName}', ${JSON.stringify(value)}, ${variableName});`);
      }
    } catch (error) {
      lines.push(`    if (DEBUG) console.log('设置属性:', '${propertyName}', '[无法序列化]', ${variableName});`);
    }

    return lines;
  }

  /**
   * 生成滤镜属性代码
   */
  private generateFiltersPropertyCode(value: any): string[] {
    const variableName = this.generateVariableName();
    const lines: string[] = [];

    if (!Array.isArray(value)) {
      lines.push(`    // 跳过非数组的滤镜属性`);
      return lines;
    }

    lines.push(`    // 滤镜属性不生成代码 (运行时动态应用)`);
    lines.push(`    // 滤镜数量: ${value.length}`);

    if (value.length > 0) {
      const filterTypes = value.map(f => f.constructor?.name || 'Unknown').join(', ');
      lines.push(`    // 滤镜类型: ${filterTypes}`);
    }

    return lines;
  }

  /**
   * 生成颜色属性代码
   */
  private generateColorPropertyCode(propertyName: string, value: number[]): string[] {
    const variableName = this.generateVariableName();
    const lines: string[] = [];

    switch (propertyName) {
      case 'colorTone':
        lines.push(`    // 设置色调`);
        lines.push(`    if (${variableName}.setColorTone) {`);
        lines.push(`        ${variableName}.setColorTone(${JSON.stringify(value)});`);
        lines.push(`    }`);
        lines.push(`    if (DEBUG) console.log('设置属性 ${propertyName} =', ${JSON.stringify(value)});`);
        break;

      case 'blendColor':
        lines.push(`    // 设置混合颜色`);
        lines.push(`    if (${variableName}.setBlendColor) {`);
        lines.push(`        ${variableName}.setBlendColor(${JSON.stringify(value)});`);
        lines.push(`    }`);
        lines.push(`    if (DEBUG) console.log('设置属性 ${propertyName} =', ${JSON.stringify(value)});`);
        break;

      default:
        lines.push(...this.generateBasicPropertyCode(propertyName, value));
        break;
    }

    return lines;
  }

  /**
   * 生成bitmap.elements代码
   */
  private generateBitmapElementsCode(value: any[]): string[] {
    const variableName = this.generateVariableName();
    const lines: string[] = [];

    if (!Array.isArray(value)) {
      return lines;
    }

    // 检查是否包含图片元素
    const hasImageElements = value.some(element => element && element.type === 'image');

    if (hasImageElements) {
      lines.push(`    // 设置包含图片的 elements 数组`);

      // 清理elements数组，移除source对象避免无效图像错误
      const cleanedElements = value.map(element => {
        if (element && element.type === 'image' && element.source) {
          // 创建清理后的元素，移除source对象
          const cleanElement = { ...element };
          delete cleanElement.source;
          return cleanElement;
        }
        return element;
      });

      const valueStr = JSON.stringify(cleanedElements);
      lines.push(`    ${variableName}._bitmap.elements = ${valueStr};`);

      // 收集所有唯一的图片路径
      const uniqueImages = new Map<string, number[]>();
      value.forEach((element, index) => {
        if (element && element.type === 'image' && element.source && element.source._url) {
          const url = element.source._url;
          if (!uniqueImages.has(url)) {
            uniqueImages.set(url, []);
          }
          uniqueImages.get(url)!.push(index);
        }
      });

      // 为每个唯一的图片路径生成加载代码
      uniqueImages.forEach((elementIndices, url) => {
        const firstIndex = elementIndices[0];
        lines.push(`    // 加载图片: ${url} (用于元素 ${elementIndices.join(', ')})`);
        lines.push(`    const imagePath_${firstIndex} = "${url}";`);
        lines.push(`    const imageBitmap_${firstIndex} = ImageManager.loadBitmapFromUrl(imagePath_${firstIndex});`);
        lines.push(`    imageBitmap_${firstIndex}.addLoadListener(function (bitmap) {`);

        // 为所有使用相同图片的元素设置source
        elementIndices.forEach(index => {
          lines.push(`        // 设置元素 ${index} 的source为bitmap对象`);
          lines.push(`        if (${variableName}._bitmap.elements[${index}]) {`);
          lines.push(`            ${variableName}._bitmap.elements[${index}].source = bitmap;`);
          lines.push(`        }`);
        });

        lines.push(`        // 触发重绘`);
        lines.push(`        ${variableName}._bitmap.redrawing();`);
        lines.push(`        if (DEBUG) console.log('图片加载完成，已设置到 ${elementIndices.length} 个元素:', imagePath_${firstIndex});`);
        lines.push(`    });`);
      });

      lines.push(`    if (DEBUG) console.log('设置 _bitmap.elements 数组，长度:', ${variableName}._bitmap.elements.length);`);
    } else {
      // 只包含文本元素，直接设置数组
      const valueStr = JSON.stringify(value);
      lines.push(`    // 设置整个 elements 数组（仅文本）`);
      lines.push(`    ${variableName}._bitmap.elements = ${valueStr};`);
      lines.push(`    ${variableName}._bitmap.redrawing();`);
      lines.push(`    if (DEBUG) console.log('设置 _bitmap.elements 数组，长度:', ${variableName}._bitmap.elements.length);`);
    }

    return lines;
  }
  /**
   * 生成bitmap.url代码 - 单个图片URL
   */
  private generateBitmapUrlCode(value: any): string[] {
    const variableName = this.generateVariableName();
    const lines: string[] = [];

    lines.push(`    // 设置单个图片URL`);
    lines.push(`    const imageUrl = ${JSON.stringify(value)};`);
    lines.push(`    const loadedBitmap = ImageManager.loadBitmapFromUrl(imageUrl);`);
    lines.push(`    loadedBitmap.addLoadListener(function (bitmap) {`);
    lines.push(`        // 直接将加载的bitmap赋值给对象的bitmap属性`);
    lines.push(`        ${variableName}.bitmap = bitmap;`);
    lines.push(`        if (DEBUG) console.log('图片加载完成，已设置bitmap:', imageUrl);`);
    lines.push(`    });`);
    lines.push(`    if (DEBUG) console.log('开始加载图片:', imageUrl);`);

    return lines;
  }


  /**
   * 生成bitmap属性代码
   */
  private generateBitmapPropertyCode(propertyName: string, value: any): string[] {
    const variableName = this.generateVariableName();
    const lines: string[] = [];

    if (propertyName === '_bitmap.elements') {
      // 处理复杂的elements数组
      return this.generateBitmapElementsCode(value);
    } else if (propertyName === '_bitmap._url' || propertyName === 'bitmap._url') {
      // 处理单个图片URL
      return this.generateBitmapUrlCode(value);
    }

    // 处理其他bitmap属性
    const bitmapProp = propertyName.replace('_bitmap.', '').replace('bitmap.', '');
    const valueStr = typeof value === 'string' ? `"${value}"` : JSON.stringify(value);

    lines.push(`    // 设置 ${bitmapProp} 属性`);
    lines.push(`    if (${variableName}._bitmap) {`);
    lines.push(`        ${variableName}._bitmap.${bitmapProp} = ${valueStr};`);
    lines.push(`        if (DEBUG) console.log('设置文字样式 ${bitmapProp} =', ${valueStr});`);
    lines.push(`    }`);

    return lines;
  }

  /**
   * 生成修改代码
   */
  generateCode(): string {
    const modifiedProperties = this.getModifiedProperties();

    if (modifiedProperties.length === 0) {
      return '// 没有属性需要修改';
    }

    const lines: string[] = [];
    const variableName = this.generateVariableName();

    // 生成注释
    lines.push(`// 设置 ${this.className} 的 ${modifiedProperties.length} 个属性`);

    // 生成对象查找代码
    lines.push(...this.generateObjectLookupCode());

    // 生成if块开始
    lines.push(`if (${variableName}) {`);

    // 生成属性修改代码
    for (const { name, value } of modifiedProperties) {
      if (name.startsWith('_bitmap.') || name.startsWith('bitmap.')) {
        lines.push(...this.generateBitmapPropertyCode(name, value));
      } else if (name === 'colorTone' || name === 'blendColor') {
        lines.push(...this.generateColorPropertyCode(name, value));
      } else {
        lines.push(...this.generateBasicPropertyCode(name, value));
      }
    }

    // 生成if块结束
    lines.push(`}`);

    return lines.join('\n');
  }

  /**
   * 获取对象路径
   */
  getObjectPath(): string[] {
    return [...this.objectPath];
  }

  /**
   * 获取类名
   */
  getClassName(): string {
    return this.className;
  }

  /**
   * 获取操作类型
   */
  getOperationType(): 'modify' | 'create' | 'delete' {
    return this.operationType;
  }

  /**
   * 获取对象的唯一键
   */
  getUniqueKey(): string {
    return `${this.className}_${this.objectPath.join('_')}`;
  }
}