import { A<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>u<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ty<PERSON><PERSON>, Button, Tooltip, IconButton } from '@mui/material';
import { useState, MouseEvent, useCallback, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import useProjectStore from '../../store/Store';
// import { saveAllModifications } from '../../services/backendServer/BackendService';
import UndoIcon from '@mui/icons-material/Undo';
import RedoIcon from '@mui/icons-material/Redo';

// 不再需要外部传入的 onOpenProject 函数
// interface MenuBarProps { }

const MenuBar = () => {
  const [fileAnchorEl, setFileAnchorEl] = useState<null | HTMLElement>(null);
  const fileMenuOpen = Boolean(fileAnchorEl);
  const projectName = useProjectStore(state => state.projectName);
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);

  // 使用全局状态管理
  const setProjectSelected = useProjectStore(state => state.setProjectSelected);
  const emit = useProjectStore(state => state.emit);

  // 定期检查撤销/重做状态
  useEffect(() => {
    const checkUndoRedoStatus = () => {
      try {
        // 查找 HistoryPanel 组件
        const historyPanel = document.querySelector('.history-panel');
        if (historyPanel) {
          // 检查撤销按钮状态
          const undoButton = historyPanel.querySelector('.undo-button');
          if (undoButton) {
            setCanUndo(!(undoButton as HTMLButtonElement).disabled);
          }

          // 检查重做按钮状态
          const redoButton = historyPanel.querySelector('.redo-button');
          if (redoButton) {
            setCanRedo(!(redoButton as HTMLButtonElement).disabled);
          }
        }
      } catch (error) {
        console.error('检查撤销/重做状态失败:', error);
      }
    };

    // 初始检查
    checkUndoRedoStatus();

    // 每秒检查一次
    const intervalId = setInterval(checkUndoRedoStatus, 1000);

    return () => clearInterval(intervalId);
  }, []);

  // 处理撤销操作
  const handleUndo = () => {
    try {
      // 查找 HistoryPanel 组件
      const historyPanel = document.querySelector('.history-panel');
      if (historyPanel) {
        // 触发撤销按钮点击
        const undoButton = historyPanel.querySelector('.undo-button');
        if (undoButton) {
          (undoButton as HTMLElement).click();
          console.log('MenuBar: 触发撤销操作');
          return;
        }
      }

      // 如果找不到按钮，通过事件系统触发撤销
      emit('trigger-undo');
      console.log('MenuBar: 通过事件系统触发撤销操作');
    } catch (error) {
      console.error('撤销操作失败:', error);
    }
  };

  // 处理重做操作
  const handleRedo = () => {
    try {
      // 查找 HistoryPanel 组件
      const historyPanel = document.querySelector('.history-panel');
      if (historyPanel) {
        // 触发重做按钮点击
        const redoButton = historyPanel.querySelector('.redo-button');
        if (redoButton) {
          (redoButton as HTMLElement).click();
          console.log('MenuBar: 触发重做操作');
          return;
        }
      }

      // 如果找不到按钮，通过事件系统触发重做
      emit('trigger-redo');
      console.log('MenuBar: 通过事件系统触发重做操作');
    } catch (error) {
      console.error('重做操作失败:', error);
    }
  };

  // 处理保存项目 - 主要保存功能
  const handleSaveProject = useCallback(async () => {
    console.log("MenuBar: 保存项目...", projectName);
    try {
      if (!projectName) {
        throw new Error('未选择项目，无法保存');
      }

      // 保存原型修改
      console.log("MenuBar: 保存原型修改...");

      // 先设置当前项目名称，确保后端状态与前端同步
      console.log("MenuBar: 设置当前项目名称:", projectName);
      await invoke('set_current_project', { projectName });

      // 调用新的保存逻辑（包含操作记录保存）
      console.log("MenuBar: 调用保存所有修改...");
      // const result = await saveAllModifications();

      // console.log(`MenuBar: 保存结果: ${result}`);
      console.log("MenuBar: 原型修改已保存");

      // 显示保存成功提示
      alert("保存成功！");
    } catch (error) {
      console.error("MenuBar: 保存失败:", error);
      alert("保存失败：" + (error as Error).message);
    }
  }, [projectName]);

  // 监听 Ctrl+S 快捷键
  useEffect(() => {
    const handleKeyDown = async (e: KeyboardEvent) => {
      if (e.ctrlKey && e.key === "s") {
        console.log("MenuBar: Ctrl+S 快捷键被触发");
        e.preventDefault();

        // 调用保存项目函数
        await handleSaveProject();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [handleSaveProject]);

  // 将保存函数暴露给全局，以便其他组件可以调用
  // useEffect(() => {
  //   // 暴露保存项目函数
  //   (window as any).saveProject = handleSaveProject;

  //   // 暴露保存属性函数，替代原来的 saveRPGEditorProperties
  //   (window as any).saveRPGEditorProperties = saveModifiedProperties;

  //   return () => {
  //     delete (window as any).saveProject;
  //     delete (window as any).saveRPGEditorProperties;
  //   };
  // }, [handleSaveProject, saveModifiedProperties]);

  const handleFileClick = (event: MouseEvent<HTMLElement>) => {
    setFileAnchorEl(event.currentTarget);
  };

  const handleFileClose = () => {
    setFileAnchorEl(null);
  };

  const handleOpenClick = async () => {
    handleFileClose();
    console.log("MenuBar - 打开项目");

    try {
      // 调用后端 API 退出项目，将临时插件复制到项目目录
      if (projectName) {
        const result = await invoke<string>('exit_project');
        console.log('退出项目结果:', result);
      }

      // 返回到项目选择界面
      setProjectSelected(false);

      // 清除 localStorage 中的项目名称
      localStorage.removeItem('rpgeditor_current_project');
    } catch (error) {
      console.error('退出项目失败:', error);

      // 即使失败也返回项目选择界面
      setProjectSelected(false);
    }
  };

  const handleSaveClick = () => {
    handleFileClose();
    // 调用保存项目函数
    handleSaveProject();
  };

  return (
    <AppBar position="static" color="default" elevation={1} sx={{ zIndex: 1100 }}>
      <Toolbar variant="dense">
        <Typography
          variant="subtitle1"
          component="div"
          onClick={handleFileClick}
          sx={{
            mr: 2,
            cursor: 'pointer',
            '&:hover': {
              color: 'primary.main',
            },
          }}
        >
          文件
        </Typography>
        <Menu
          anchorEl={fileAnchorEl}
          open={fileMenuOpen}
          onClose={handleFileClose}
          slotProps={{
            paper: {
              'aria-labelledby': 'file-button',
            },
          }}
        >
          <MenuItem onClick={handleOpenClick}>打开项目</MenuItem>
          <MenuItem onClick={handleSaveClick}>保存项目</MenuItem>
        </Menu>

        {/* 添加撤销/重做按钮 */}
        <Tooltip title="撤销 (Ctrl+Z)">
          <span>
            <IconButton
              color="primary"
              onClick={handleUndo}
              disabled={!canUndo}
              sx={{ ml: 1 }}
            >
              <UndoIcon />
            </IconButton>
          </span>
        </Tooltip>

        <Tooltip title="重做 (Ctrl+Y)">
          <span>
            <IconButton
              color="primary"
              onClick={handleRedo}
              disabled={!canRedo}
              sx={{ ml: 1 }}
            >
              <RedoIcon />
            </IconButton>
          </span>
        </Tooltip>

        {projectName && (
          <Typography variant="subtitle1" component="div" sx={{ ml: 'auto' }}>
            当前项目: {projectName}
          </Typography>
        )}
      </Toolbar>
    </AppBar>
  );
};

export default MenuBar;
