import React, { useState, useEffect, memo } from "react";
import { Box, Typography, Slider, Grid, Select, MenuItem, FormControl, InputLabel } from "@mui/material";
import NumberInput from "../../ui/NumberInput";
import ColorPicker from "../../ui/ColorPicker";
import useProjectStore from "../../../store/Store";

interface SpriteColorGroupProps {
  selectedObject: any;
  forceUpdate: () => void;
}

const SpriteColorGroup: React.FC<SpriteColorGroupProps> = memo(({ selectedObject, forceUpdate }) => {
  // 获取全局状态和Store方法
  const selectedObjectsState = useProjectStore(state => state.selectedObjects);
  const setSelectedObjectsProperty = useProjectStore(state => state.setSelectedObjectsProperty);

  // 获取选中的对象数组 - 提取真实对象
  const selectedObjects = selectedObjectsState.objects.map(obj => obj.object);

  // 使用 useState 在组件内部管理属性值
  // 混合模式
  const [blendMode, setBlendMode] = useState(0);

  // 色相
  const [hueValue, setHueValue] = useState(0);

  // 色调 [r, g, b, gray]
  const [colorToneR, setColorToneR] = useState(0);
  const [colorToneG, setColorToneG] = useState(0);
  const [colorToneB, setColorToneB] = useState(0);
  const [colorToneGray, setColorToneGray] = useState(0);

  // 混合颜色 [r, g, b, a]
  const [blendColorR, setBlendColorR] = useState(0);
  const [blendColorG, setBlendColorG] = useState(0);
  const [blendColorB, setBlendColorB] = useState(0);
  const [blendColorA, setBlendColorA] = useState(0);

  // 混合模式选项
  const blendModes = [
    { value: 0, label: "正常 (NORMAL)" },
    { value: 1, label: "相加 (ADD)" },
    { value: 2, label: "乘法 (MULTIPLY)" },
    { value: 3, label: "屏幕 (SCREEN)" },
    { value: 4, label: "叠加 (OVERLAY)" },
    { value: 5, label: "变暗 (DARKEN)" },
    { value: 6, label: "变亮 (LIGHTEN)" },
    { value: 7, label: "颜色减淡 (COLOR_DODGE)" },
    { value: 8, label: "颜色加深 (COLOR_BURN)" },
    { value: 9, label: "强光 (HARD_LIGHT)" },
    { value: 10, label: "柔光 (SOFT_LIGHT)" },
    { value: 11, label: "差值 (DIFFERENCE)" },
    { value: 12, label: "排除 (EXCLUSION)" },
    { value: 13, label: "色相 (HUE)" },
    { value: 14, label: "饱和度 (SATURATION)" },
    { value: 15, label: "颜色 (COLOR)" },
    { value: 16, label: "明度 (LUMINOSITY)" },
    { value: 17, label: "正片叠底 (NORMAL_NPM)" },
    { value: 18, label: "相加 (ADD_NPM)" },
    { value: 19, label: "屏幕 (SCREEN_NPM)" }
  ];

  // 检查对象是否是 Sprite 或其子类
  const isSprite = selectedObject &&
    (selectedObject.constructor?.name === 'Sprite' ||
      selectedObject.constructor?.name?.includes('Sprite_') ||
      (selectedObject._bitmap !== undefined) ||
      typeof selectedObject.setHue === 'function' ||
      typeof selectedObject.setColorTone === 'function' ||
      typeof selectedObject.setBlendColor === 'function');

  // 当选中对象变化时，更新本地状态
  useEffect(() => {
    if (selectedObject && isSprite) {
      // 尝试获取混合模式
      try {
        if (selectedObject.blendMode !== undefined) {
          setBlendMode(selectedObject.blendMode);
        } else if (selectedObject._blendMode !== undefined) {
          setBlendMode(selectedObject._blendMode);
        }
      } catch (error) {
        console.error("获取混合模式时出错:", error);
        setBlendMode(0);
      }

      // 尝试获取色相值
      try {
        // 如果对象有 _hue 属性
        if (selectedObject._hue !== undefined) {
          setHueValue(selectedObject._hue);
        }
        // 如果对象有 getHue 方法
        else if (typeof selectedObject.getHue === 'function') {
          setHueValue(selectedObject.getHue());
        }
      } catch (error) {
        console.error("获取色相值时出错:", error);
        setHueValue(0);
      }

      // 尝试获取色调值
      try {
        // 如果对象有 _colorTone 属性
        if (selectedObject._colorTone && Array.isArray(selectedObject._colorTone)) {
          setColorToneR(selectedObject._colorTone[0] || 0);
          setColorToneG(selectedObject._colorTone[1] || 0);
          setColorToneB(selectedObject._colorTone[2] || 0);
          setColorToneGray(selectedObject._colorTone[3] || 0);
        }
        // 如果对象有 getColorTone 方法
        else if (typeof selectedObject.getColorTone === 'function') {
          const colorTone = selectedObject.getColorTone();
          if (Array.isArray(colorTone)) {
            setColorToneR(colorTone[0] || 0);
            setColorToneG(colorTone[1] || 0);
            setColorToneB(colorTone[2] || 0);
            setColorToneGray(colorTone[3] || 0);
          }
        }
      } catch (error) {
        console.error("获取色调值时出错:", error);
        setColorToneR(0);
        setColorToneG(0);
        setColorToneB(0);
        setColorToneGray(0);
      }

      // 尝试获取混合颜色值
      try {
        // 如果对象有 _blendColor 属性
        if (selectedObject._blendColor && Array.isArray(selectedObject._blendColor)) {
          setBlendColorR(selectedObject._blendColor[0] || 0);
          setBlendColorG(selectedObject._blendColor[1] || 0);
          setBlendColorB(selectedObject._blendColor[2] || 0);
          setBlendColorA(selectedObject._blendColor[3] || 0);
        }
        // 如果对象有 getBlendColor 方法
        else if (typeof selectedObject.getBlendColor === 'function') {
          const blendColor = selectedObject.getBlendColor();
          if (Array.isArray(blendColor)) {
            setBlendColorR(blendColor[0] || 0);
            setBlendColorG(blendColor[1] || 0);
            setBlendColorB(blendColor[2] || 0);
            setBlendColorA(blendColor[3] || 0);
          }
        }
      } catch (error) {
        console.error("获取混合颜色值时出错:", error);
        setBlendColorR(0);
        setBlendColorG(0);
        setBlendColorB(0);
        setBlendColorA(0);
      }
    }
  }, [selectedObject, isSprite]);

  // 处理混合模式变化
  const handleBlendModeChange = async (value: number) => {
    if (selectedObjects.length > 0 && isSprite) {
      try {
        // 更新本地状态
        setBlendMode(value);

        // 使用Store中的统一方法更新属性
        setSelectedObjectsProperty("blendMode", value);
      } catch (error) {
        console.error("修改混合模式时出错:", error);
      }
    }
  };

  // 处理色相变化
  const handleHueChange = async (value: number) => {
    if (selectedObjects.length > 0 && isSprite) {
      try {
        // 更新本地状态
        setHueValue(value);

        // 先使用 setHue 方法修改色相
        if (typeof selectedObject.setHue === 'function') {
          selectedObjects.forEach((obj: any) => {
            if (typeof obj.setHue === 'function') {
              obj.setHue(value);
            }
          });
        }

        // 使用Store中的统一方法更新属性
        setSelectedObjectsProperty("hue", value);
      } catch (error) {
        console.error("修改色相值时出错:", error);
      }
    }
  };

  // 处理色调变化
  const handleColorToneChange = async (index: number, value: number) => {
    if (selectedObjects.length > 0 && isSprite) {
      try {
        // 更新本地状态
        switch (index) {
          case 0:
            setColorToneR(value);
            break;
          case 1:
            setColorToneG(value);
            break;
          case 2:
            setColorToneB(value);
            break;
          case 3:
            setColorToneGray(value);
            break;
        }

        // 构建新的色调数组
        const newColorTone = [
          index === 0 ? value : colorToneR,
          index === 1 ? value : colorToneG,
          index === 2 ? value : colorToneB,
          index === 3 ? value : colorToneGray
        ];

        // 先使用 setColorTone 方法修改色调
        if (typeof selectedObject.setColorTone === 'function') {
          selectedObjects.forEach((obj: any) => {
            if (typeof obj.setColorTone === 'function') {
              obj.setColorTone(newColorTone);
            }
          });
        }

        // 使用Store中的统一方法更新属性
        setSelectedObjectsProperty("colorTone", newColorTone);
      } catch (error) {
        console.error("修改色调值时出错:", error);
      }
    }
  };

  // 处理混合颜色变化
  const handleBlendColorChange = async (index: number, value: number) => {
    if (selectedObjects.length > 0 && isSprite) {
      try {
        // 更新本地状态
        switch (index) {
          case 0:
            setBlendColorR(value);
            break;
          case 1:
            setBlendColorG(value);
            break;
          case 2:
            setBlendColorB(value);
            break;
          case 3:
            setBlendColorA(value);
            break;
        }

        // 构建新的混合颜色数组
        const newBlendColor = [
          index === 0 ? value : blendColorR,
          index === 1 ? value : blendColorG,
          index === 2 ? value : blendColorB,
          index === 3 ? value : blendColorA
        ];

        // 先使用 setBlendColor 方法修改混合颜色
        if (typeof selectedObject.setBlendColor === 'function') {
          selectedObjects.forEach((obj: any) => {
            if (typeof obj.setBlendColor === 'function') {
              obj.setBlendColor(newBlendColor);
            }
          });
        }

        // 使用Store中的统一方法更新属性
        setSelectedObjectsProperty("blendColor", newBlendColor);
      } catch (error) {
        console.error("修改混合颜色值时出错:", error);
      }
    }
  };

  // 如果不是 Sprite 或其子类，不显示此组件
  if (!isSprite) {
    return null;
  }

  return (
    <Box sx={{ mb: 2 }}>
      <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
        Sprite 颜色设置
      </Typography>

      {/* 混合模式设置 */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2" sx={{ mb: 0.5 }}>
          混合模式 (Blend Mode):
        </Typography>
        <FormControl fullWidth size="small">
          <Select
            value={blendMode}
            onChange={(e) => handleBlendModeChange(e.target.value as number)}
            displayEmpty
            sx={{ mb: 1 }}
          >
            {blendModes.map((mode) => (
              <MenuItem key={mode.value} value={mode.value}>
                {mode.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      {/* 色相设置 */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2" sx={{ mb: 0.5 }}>
          色相 (Hue): {hueValue}
        </Typography>
        <Slider
          value={hueValue}
          onChange={(_, value) => handleHueChange(value as number)}
          min={-360}
          max={360}
          step={1}
          valueLabelDisplay="auto"
          aria-labelledby="hue-slider"
        />
        <NumberInput
          label=""
          value={hueValue}
          onChange={(value) => handleHueChange(value)}
          width="100%"
          min={-360}
          max={360}
          step={1}
        />
      </Box>

      {/* 色调设置 */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2" sx={{ mb: 0.5 }}>
          色调 (Color Tone):
        </Typography>
        <Grid container spacing={1}>
          <Grid item xs={6}>
            <Typography variant="caption">红 (R): {colorToneR}</Typography>
            <Slider
              value={colorToneR}
              onChange={(_, value) => handleColorToneChange(0, value as number)}
              min={-255}
              max={255}
              step={1}
              valueLabelDisplay="auto"
            />
          </Grid>
          <Grid item xs={6}>
            <Typography variant="caption">绿 (G): {colorToneG}</Typography>
            <Slider
              value={colorToneG}
              onChange={(_, value) => handleColorToneChange(1, value as number)}
              min={-255}
              max={255}
              step={1}
              valueLabelDisplay="auto"
            />
          </Grid>
          <Grid item xs={6}>
            <Typography variant="caption">蓝 (B): {colorToneB}</Typography>
            <Slider
              value={colorToneB}
              onChange={(_, value) => handleColorToneChange(2, value as number)}
              min={-255}
              max={255}
              step={1}
              valueLabelDisplay="auto"
            />
          </Grid>
          <Grid item xs={6}>
            <Typography variant="caption">灰度 (Gray): {colorToneGray}</Typography>
            <Slider
              value={colorToneGray}
              onChange={(_, value) => handleColorToneChange(3, value as number)}
              min={-255}
              max={255}
              step={1}
              valueLabelDisplay="auto"
            />
          </Grid>
        </Grid>
      </Box>

      {/* 混合颜色设置 */}
      <Box sx={{ mb: 1 }}>
        <Typography variant="body2" sx={{ mb: 0.5 }}>
          混合颜色 (Blend Color):
        </Typography>
        <Grid container spacing={1}>
          <Grid item xs={6}>
            <Typography variant="caption">红 (R): {blendColorR}</Typography>
            <Slider
              value={blendColorR}
              onChange={(_, value) => handleBlendColorChange(0, value as number)}
              min={0}
              max={255}
              step={1}
              valueLabelDisplay="auto"
            />
          </Grid>
          <Grid item xs={6}>
            <Typography variant="caption">绿 (G): {blendColorG}</Typography>
            <Slider
              value={blendColorG}
              onChange={(_, value) => handleBlendColorChange(1, value as number)}
              min={0}
              max={255}
              step={1}
              valueLabelDisplay="auto"
            />
          </Grid>
          <Grid item xs={6}>
            <Typography variant="caption">蓝 (B): {blendColorB}</Typography>
            <Slider
              value={blendColorB}
              onChange={(_, value) => handleBlendColorChange(2, value as number)}
              min={0}
              max={255}
              step={1}
              valueLabelDisplay="auto"
            />
          </Grid>
          <Grid item xs={6}>
            <Typography variant="caption">透明度 (A): {blendColorA}</Typography>
            <Slider
              value={blendColorA}
              onChange={(_, value) => handleBlendColorChange(3, value as number)}
              min={0}
              max={1}
              step={0.01}
              valueLabelDisplay="auto"
            />
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
}, (prevProps, nextProps) => {
  // 只有当selectedObject发生变化时才重新渲染
  return prevProps.selectedObject === nextProps.selectedObject;
});

export default SpriteColorGroup;
