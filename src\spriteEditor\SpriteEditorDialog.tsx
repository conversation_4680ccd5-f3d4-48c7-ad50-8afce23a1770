import React, { useEffect, useRef, useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  IconButton,
  Typography
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import SaveIcon from '@mui/icons-material/Save';
import FolderOpenIcon from '@mui/icons-material/FolderOpen';
import { open as openDialog } from '@tauri-apps/plugin-dialog';
import { invoke } from '@tauri-apps/api/core';
import useProjectStore from '../store/Store';
import { spriteToElements, elementsToSprite } from '../utils/spriteDataConverter';
import { setObjectProperty } from '../services';

interface SpriteEditorDialogProps {
  open: boolean;
  onClose: () => void;
  sprite?: any;
  resourcePath?: string;
  onApply?: (data: any) => void;
}

const SpriteEditorDialog: React.FC<SpriteEditorDialogProps> = ({
  open,
  onClose,
  sprite,
  onApply
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [isIframeLoaded, setIsIframeLoaded] = useState(false);
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [originalElements, setOriginalElements] = useState<any[]>([]);

  // 获取当前项目信息
  const { projectName } = useProjectStore();

  // 深度比较两个elements数组，检测变化
  const detectElementsChanges = (originalElements: any[], newElements: any[]) => {
    const changes: Array<{
      type: 'added' | 'removed' | 'modified';
      elementType: 'text' | 'image';
      index: number;
      element?: any;
      oldElement?: any;
      newElement?: any;
      changedProperties?: string[];
    }> = [];

    // 创建元素映射，用于快速查找
    const originalMap = new Map();
    const newMap = new Map();

    // 为原始元素创建唯一标识
    originalElements.forEach((element, index) => {
      const key = element.type === 'text'
        ? `text_${element.x}_${element.y}_${element.text}`
        : `image_${element.dx}_${element.dy}_${element.dw}_${element.dh}`;
      originalMap.set(key, { element, index });
    });

    // 为新元素创建唯一标识
    newElements.forEach((element, index) => {
      const key = element.type === 'text'
        ? `text_${element.x}_${element.y}_${element.text}`
        : `image_${element.dx}_${element.dy}_${element.dw}_${element.dh}`;
      newMap.set(key, { element, index });
    });

    // 检测新增的元素
    newMap.forEach((newItem, key) => {
      if (!originalMap.has(key)) {
        changes.push({
          type: 'added',
          elementType: newItem.element.type,
          index: newItem.index,
          element: newItem.element
        });
      }
    });

    // 检测删除的元素
    originalMap.forEach((originalItem, key) => {
      if (!newMap.has(key)) {
        changes.push({
          type: 'removed',
          elementType: originalItem.element.type,
          index: originalItem.index,
          element: originalItem.element
        });
      }
    });

    // 检测修改的元素（基于位置匹配）
    originalElements.forEach((originalElement, originalIndex) => {
      const matchingNewElement = newElements.find((newElement, newIndex) => {
        if (originalElement.type !== newElement.type) return false;

        if (originalElement.type === 'text') {
          // 文本元素：基于位置匹配
          return Math.abs(originalElement.x - newElement.x) < 5 &&
            Math.abs(originalElement.y - newElement.y) < 5;
        } else {
          // 图像元素：基于位置匹配
          return Math.abs(originalElement.dx - newElement.dx) < 5 &&
            Math.abs(originalElement.dy - newElement.dy) < 5;
        }
      });

      if (matchingNewElement) {
        const changedProperties = [];

        // 检测属性变化
        if (originalElement.type === 'text') {
          if (originalElement.text !== matchingNewElement.text) changedProperties.push('text');
          if (originalElement.x !== matchingNewElement.x) changedProperties.push('x');
          if (originalElement.y !== matchingNewElement.y) changedProperties.push('y');
          if (originalElement.maxWidth !== matchingNewElement.maxWidth) changedProperties.push('maxWidth');
          if (originalElement.lineHeight !== matchingNewElement.lineHeight) changedProperties.push('lineHeight');
          if (originalElement.align !== matchingNewElement.align) changedProperties.push('align');
        } else {
          if (originalElement.dx !== matchingNewElement.dx) changedProperties.push('dx');
          if (originalElement.dy !== matchingNewElement.dy) changedProperties.push('dy');
          if (originalElement.dw !== matchingNewElement.dw) changedProperties.push('dw');
          if (originalElement.dh !== matchingNewElement.dh) changedProperties.push('dh');
          if (originalElement.sx !== matchingNewElement.sx) changedProperties.push('sx');
          if (originalElement.sy !== matchingNewElement.sy) changedProperties.push('sy');
          if (originalElement.sw !== matchingNewElement.sw) changedProperties.push('sw');
          if (originalElement.sh !== matchingNewElement.sh) changedProperties.push('sh');
        }

        if (changedProperties.length > 0) {
          changes.push({
            type: 'modified',
            elementType: originalElement.type,
            index: originalIndex,
            oldElement: originalElement,
            newElement: matchingNewElement,
            changedProperties
          });
        }
      }
    });

    return changes;
  };

  // 清理elements数组中的循环引用，使其可以安全地序列化为JSON
  const cleanElementsForSerialization = (elements: any[]) => {
    return elements.map(element => {
      const cleanElement = { ...element };

      if (element.type === 'image' && element.source) {
        // 对于图像元素，只保留必要的source信息
        cleanElement.source = {
          _url: element.source._url,
          width: element.source.width,
          height: element.source.height,
          // 移除所有可能导致循环引用的复杂对象
        };
      }

      return cleanElement;
    });
  };

  // 保存elements变化到后端
  const saveElementsChangesToBackend = async (changes: any[]) => {
    if (!sprite || changes.length === 0) {
      console.log('没有变化需要保存');
      return;
    }

    console.log('检测到的变化:', changes);

    try {
      // 为每个变化创建后端记录
      for (const change of changes) {
        let fieldName = '';
        let newValue = null;

        switch (change.type) {
          case 'added':
            // 新增元素：记录整个elements数组（清理后）
            fieldName = '_bitmap.elements';
            newValue = cleanElementsForSerialization(sprite.bitmap.elements);
            break;

          case 'removed':
            // 删除元素：记录整个elements数组（清理后）
            fieldName = '_bitmap.elements';
            newValue = cleanElementsForSerialization(sprite.bitmap.elements);
            break;

          case 'modified':
            // 修改元素：根据变化的属性记录
            if (change.changedProperties && change.changedProperties.length > 0) {
              for (const property of change.changedProperties) {
                fieldName = `_bitmap.elements[${change.index}].${property}`;
                newValue = change.newElement[property];

                // 如果是source属性，需要清理
                if (property === 'source' && newValue && typeof newValue === 'object') {
                  newValue = {
                    _url: newValue._url,
                    width: newValue.width,
                    height: newValue.height,
                  };
                }

                // 记录单个属性修改
                try {
                  setObjectProperty(
                    (sprite as any)._rpgEditorPath || ['Unknown'],
                    sprite.constructor?.name || 'Sprite',
                    fieldName,
                    newValue
                  );
                  console.log(`已记录属性修改: ${fieldName} = ${JSON.stringify(newValue)}`);
                } catch (error) {
                  console.error(`记录属性修改失败:`, error);
                }
              }
              continue; // 跳过下面的通用记录
            }
            break;
        }

        // 记录通用变化（新增、删除、或没有具体属性的修改）
        if (fieldName) {
          try {
            setObjectProperty(
              (sprite as any)._rpgEditorPath || ['Unknown'],
              sprite.constructor?.name || 'Sprite',
              fieldName,
              newValue
            );
            console.log(`已记录变化: ${change.type} - ${fieldName} - ${JSON.stringify(newValue)}`);
          } catch (error) {
            console.error(`记录变化失败:`, error);
          }
        }
      }

      console.log('所有变化已成功保存到后端');
    } catch (error) {
      console.error('保存变化到后端时出错:', error);
    }
  };

  // 根据文件路径读取文件并转换为ArrayBuffer
  const convertFilePathToArrayBuffer = async (filePath: string, callback?: (buffer: ArrayBuffer | null) => void): Promise<ArrayBuffer | null> => {
    try {
      console.log('正在读取文件:', filePath);
      const fileContent = await invoke<number[]>('read_file_as_bytes', { filePath });
      const arrayBuffer = new Uint8Array(fileContent).buffer;
      console.log('文件读取成功，大小:', arrayBuffer.byteLength, 'bytes');

      if (callback) {
        callback(arrayBuffer);
      }

      return arrayBuffer;
    } catch (error) {
      console.error('读取文件失败:', error);

      if (callback) {
        callback(null);
      }

      return null;
    }
  };

  // 处理文件选择对话框
  const handleFileSelectDialog = async (callback?: (filePath: string | null, arrayBuffer?: ArrayBuffer) => void) => {
    try {
      // 获取当前项目路径
      const projectPath = await invoke<string>('get_project_path', { projectName });
      console.log('项目路径:', projectPath);

      // 打开文件选择对话框
      const selected = await openDialog({
        directory: false,
        multiple: false,
        title: '选择文件',
        defaultPath: projectPath,
        filters: [
          {
            name: '图片文件',
            extensions: ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp']
          },
          {
            name: '音频文件',
            extensions: ['mp3', 'wav', 'ogg', 'm4a']
          },
          {
            name: '视频文件',
            extensions: ['mp4', 'webm', 'ogv']
          },
          {
            name: '数据文件',
            extensions: ['json', 'txt', 'js']
          },
          {
            name: '所有文件',
            extensions: ['*']
          }
        ]
      });

      if (selected) {
        const filePath = selected as string;

        // 检查选择的文件是否在当前项目目录内
        if (filePath.includes(projectPath)) {
          // 获取相对于项目的路径
          const relativePath = filePath.replace(projectPath + '\\', '').replace(/\\/g, '/');
          setSelectedFile(relativePath);
          console.log('选择的文件:', relativePath);

          try {
            // 读取文件内容并转换为ArrayBuffer
            console.log('正在读取文件:', filePath);
            const fileContent = await invoke<number[]>('read_file_as_bytes', { filePath });
            const arrayBuffer = new Uint8Array(fileContent).buffer;
            console.log('文件读取成功，大小:', arrayBuffer.byteLength, 'bytes');

            // 如果有回调函数，调用它并传递选择的文件路径和ArrayBuffer
            if (callback) {
              callback("../projects/" + projectName + '/' + relativePath, arrayBuffer);
            }

            return { path: relativePath, buffer: arrayBuffer };
          } catch (fileError) {
            console.error('读取文件失败:', fileError);
            // 即使读取文件失败，也传递文件路径，ArrayBuffer为undefined
            if (callback) {
              callback(relativePath, undefined);
            }
            return { path: relativePath, buffer: undefined };
          }
        } else {
          console.warn('选择的文件不在当前项目目录内');
          alert('请选择当前项目目录内的文件');
          if (callback) {
            callback(null, undefined);
          }
          return null;
        }
      } else {
        // 用户取消了选择
        if (callback) {
          callback(null, undefined);
        }
        return null;
      }
    } catch (error) {
      console.error('打开文件选择对话框失败:', error);
      if (callback) {
        callback(null, undefined);
      }
      return null;
    }
  };

  // 处理文件选择后的保存操作
  const handleFileSelectAndSave = () => {
    if (!selectedFile) {
      console.warn('未选择文件');
      return;
    }

    // 这里可以添加将选择的文件应用到sprite的逻辑
    console.log('应用选择的文件:', selectedFile);
    // 在Tauri主应用中调用
    // iframe.contentWindow.SpriteEditor.loadExternalImageFile('/path/to/image.png');
    // 调用原有的保存逻辑
    // handleSave();
  };

  // 当iframe加载完成时的处理
  const handleIframeLoad = () => {
    console.log('SpriteEditor iframe 加载完成');
    setIsIframeLoaded(true);

    // 确保iframe内容已加载并且window.SpriteEditor存在
    const iframe = iframeRef.current;
    if (iframe && iframe.contentWindow) {

      // useEffect(() => {
      //   // 在这里执行需要在iframe加载完成后执行的逻辑
      //   console.log('SpriteEditor iframe 加载完成，执行后续逻辑');
      // }, []);
      // 等待一小段时间确保iframe内的脚本已执行

      const iframeWindow = iframe.contentWindow as any;
      // 监听保存事件
      iframeWindow.document.addEventListener('spriteEditorInitialized', async (event: any) => {
        console.log('spriteEditorInitialized事件触发');
        // 绑定文件选择方法到iframe窗口
        iframeWindow.handleFileSelectDialog = handleFileSelectDialog;
        console.log('已绑定文件选择方法到iframe窗口');

        // 绑定文件路径转ArrayBuffer方法到iframe窗口
        iframeWindow.convertFilePathToArrayBuffer = convertFilePathToArrayBuffer;
        console.log('已绑定文件路径转ArrayBuffer方法到iframe窗口');

        iframeWindow.SpriteEditor.globalFonts = ["rmmz-mainfont", "Microsoft Yahei", "PingFang SC", "sans-serif"]
        // 检查是否存在SpriteEditor对象
        if (iframeWindow.SpriteEditor) {
          console.log('找到 SpriteEditor 对象，准备传递数据');

          // 传递sprite数据
          if (sprite && iframeWindow.SpriteEditor.setExternalSpriteForEdit) {
            try {
              const elems = await spriteToElements(sprite);
              console.log("外部转化", elems);

              // 保存原始elements数据用于变化检测（清理后）
              setOriginalElements(cleanElementsForSerialization(elems.elements));

              iframeWindow.SpriteEditor.setExternalSpriteForEdit(elems.elements);
              console.log('已传递sprite数据:', sprite);
            } catch (error) {
              console.error('转换sprite数据时出错:', error);
            }
          }



          // 监听保存事件
          iframeWindow.document.addEventListener('spriteEditorSave', async (event: any) => {
            try {
              // 获取保存的数据
              const newElements = event.detail.elements;
              console.log('外部收到sprite编辑器保存事件:', newElements);

              // 检测变化
              const changes = detectElementsChanges(originalElements, newElements);
              console.log('检测到的变化:', changes);

              // 更新sprite
              const sp = await elementsToSprite(newElements, newElements.spriteInfo);
              sprite.bitmap = sp.bitmap;
              sprite.bitmap.redrawing();

              // 保存变化到后端
              if (changes.length > 0) {
                await saveElementsChangesToBackend(changes);
                console.log('变化已保存到后端');
              } else {
                console.log('没有检测到变化');
              }

              // 更新原始elements数据（清理后）
              setOriginalElements(cleanElementsForSerialization(newElements));

            } catch (error) {
              console.error('处理sprite编辑器保存事件时出错:', error);
            }
          });
          // 传递资源路径
          if (iframeWindow.SpriteEditor.setExternalResourcePathForAccess) {
            iframeWindow.SpriteEditor.setExternalResourcePathForAccess("../projects/Project3/");
            console.log('已传递资源路径:', "../projects/Project3/");
          }
        } else {
          console.warn('iframe中未找到 SpriteEditor 对象');
        }
      })
      // 延迟500ms确保iframe内容完全加载
    }
  }
  // 处理关闭操作
  const handleClose = () => {
    setIsIframeLoaded(false);
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          height: '90vh',
          maxHeight: '90vh',
          width: '110',
          maxWidth: '110'
        }
      }}
    >
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6">Sprite 编辑器</Typography>
        <IconButton onClick={handleClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0, display: 'flex', flexDirection: 'column', flex: 1 }}>
        <Box sx={{ flex: 1, overflow: 'hidden' }}>
          <iframe
            ref={iframeRef}
            src="src/spriteEditor/index.html"
            style={{
              width: '100%',
              height: '100%',
              border: 'none',
              display: 'block'
            }}
            onLoad={handleIframeLoad}
            title="Sprite Editor"
          />
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default SpriteEditorDialog;
