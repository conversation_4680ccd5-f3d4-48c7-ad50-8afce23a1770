/**
 * OperationManager - 管理所有的BaseTempObj操作
 * 使用Map存储操作，支持序列化和代码生成
 */

import { BaseTempObj } from './baseTempObj';

export class OperationManager {
  private operations: Map<string, BaseTempObj> = new Map();

  /**
   * 添加或更新操作
   */
  addOperation(objectPath: string[], className: string, operationType: 'modify' | 'create' | 'delete' = 'modify'): BaseTempObj {
    const key = this.generateKey(objectPath, className);

    let operation = this.operations.get(key);
    if (!operation) {
      operation = new BaseTempObj(objectPath, className, operationType);
      this.operations.set(key, operation);
      console.log(`OperationManager: 创建新操作 - ${className}[${objectPath.join('/')}] (${operationType})`);
    } else {
      console.log(`OperationManager: 使用现有操作 - ${className}[${objectPath.join('/')}]`);
    }

    return operation;
  }

  /**
   * 获取操作
   */
  getOperation(objectPath: string[], className: string): BaseTempObj | undefined {
    const key = this.generateKey(objectPath, className);
    return this.operations.get(key);
  }

  /**
   * 删除操作
   */
  removeOperation(objectPath: string[], className: string): boolean {
    const key = this.generateKey(objectPath, className);
    return this.operations.delete(key);
  }

  /**
   * 设置属性
   */
  setProperty(objectPath: string[], className: string, propertyName: string, value: any): void {
    console.log(`OperationManager: 设置属性 - ${className}[${objectPath.join('/')}].${propertyName} = ${JSON.stringify(value)}`);
    const operation = this.addOperation(objectPath, className);
    operation.setProperty(propertyName, value);
    console.log(`OperationManager: 属性设置完成，当前操作数量: ${this.operations.size}`);
  }

  /**
   * 获取属性
   */
  getProperty(objectPath: string[], className: string, propertyName: string): any {
    const operation = this.getOperation(objectPath, className);
    return operation?.getProperty(propertyName);
  }

  /**
   * 获取所有操作
   */
  getAllOperations(): BaseTempObj[] {
    return Array.from(this.operations.values());
  }

  /**
   * 清空所有操作
   */
  clear(): void {
    this.operations.clear();
  }

  /**
   * 获取操作数量
   */
  size(): number {
    return this.operations.size;
  }

  /**
   * 生成查找对象的方法代码
   */
  private generateObjectLookupMethods(): string {
    return `
// ==================== 对象查找方法 ====================
    // 获取插件参数
    const parameters = PluginManager.parameters("RPGEditor_PrototypeModifications");
    const DEBUG = parameters["debug"] === "true";

    // 调试日志函数
    function log(message, ...args) {
        if (DEBUG) {
            console.log("[RPGEditor]", message, ...args);
        }
    }
    // 工具函数：根据场景路径查找对象
    function findObjectByScenePath(scenePath) {
        if (!scenePath || scenePath.length === 0) return null;

        let current = SceneManager._scene;
        if (!current) return null;

        // 检查第一个元素是否为创建操作标记
        const firstElement = scenePath[0];
        let isCreationOperation = firstElement === "+";

        // 确定实际的场景路径起始位置
        let startIndex = isCreationOperation ? 1 : 0;

        // 验证场景名称
        const expectedSceneName = scenePath[startIndex];
        const actualSceneName = current.constructor.name;
        if (actualSceneName !== expectedSceneName) {
            if (DEBUG) console.log(\`[Scene Mismatch] Expected: \${expectedSceneName}, Actual: \${actualSceneName}\`);
            return null;
        }

        // 确定遍历的结束位置
        let endIndex = scenePath.length;
        if (isCreationOperation) {
            // 创建操作：最后一个索引不查找，那是要创建的位置
            endIndex = scenePath.length - 1;
            if (DEBUG) console.log(\`[Creation Operation] Finding parent object, skipping last index: \${scenePath[scenePath.length - 1]}\`);
        }

        // 遍历路径
        for (let i = startIndex + 1; i < endIndex; i++) {
            const index = parseInt(scenePath[i]);
            if (isNaN(index) || !current.children || !current.children[index]) {
                if (DEBUG) console.log(\`[Path Break] Index \${index} does not exist in \${current.constructor.name}\`);
                return null;
            }
            current = current.children[index];
        }

        return current;
    }
`;
  }

  /**
   * 生成对象创建方法代码
   */
  private generateObjectCreationMethods(): string {
    return `
    // 工具函数：创建游戏对象
    function createGameObject(type, params = {}) {
        if (DEBUG) log(创建游戏对象: type , params);

        switch (type) {
            case "Sprite":
                const sprite = new Sprite();
                sprite.name = params.name || "新Sprite";
                sprite.x = params.x || 0;
                sprite.y = params.y || 0;
                sprite.visible = params.visible !== undefined ? params.visible : true;
                return sprite;

            case "Label":
                const label = new Sprite();
                label.name = params.name || "新Label";
                label.x = params.x || 0;
                label.y = params.y || 0;
                label.visible = params.visible !== undefined ? params.visible : true;

                const bitmap = new Bitmap(200, 40);
                bitmap.fontSize = 20;
                bitmap.textColor = "#ffffff";
                bitmap.outlineColor = "rgba(0, 0, 0, 0.5)";
                bitmap.outlineWidth = 4;
                bitmap.drawText(params.text || "新文本", 0, 0, 200, 40, "left");
                bitmap.text = params.text || "新文本";

                label.bitmap = bitmap;
                return label;

            case "Container":
                const container = new PIXI.Container();
                container.name = params.name || "新Container";
                container.x = params.x || 0;
                container.y = params.y || 0;
                container.visible = params.visible !== undefined ? params.visible : true;
                return container;

            case "Window":
                const rect = new Rectangle(0, 0, 200, 100);
                const windowObj = new Window_Base(rect);
                windowObj.name = params.name || "新Window";
                windowObj.x = params.x || 0;
                windowObj.y = params.y || 0;
                windowObj.visible = params.visible !== undefined ? params.visible : true;
                return windowObj;

            case "Button":
                const button = new Sprite_Clickable();
                button.name = params.name || "新Button";
                button.x = params.x || 0;
                button.y = params.y || 0;
                button.visible = params.visible !== undefined ? params.visible : true;

                const buttonBitmap = new Bitmap(120, 40);
                buttonBitmap.fillRect(0, 0, 120, 40, "#3498db");
                buttonBitmap.fontSize = 18;
                buttonBitmap.textColor = "#ffffff";
                buttonBitmap.drawText(params.text || "按钮", 0, 0, 120, 40, "center");

                button.bitmap = buttonBitmap;
                button._isButton = true;
                return button;

            case "LayoutContainer":
                if (typeof LayoutContainer !== "undefined") {
                    const layoutContainer = new LayoutContainer();
                    layoutContainer.name = params.name || "新布局容器";
                    layoutContainer.x = params.x || 0;
                    layoutContainer.y = params.y || 0;
                    layoutContainer.visible = params.visible !== undefined ? params.visible : true;
                    return layoutContainer;
                } else {
                    // 如果没有LayoutContainer类，创建一个普通的Container
                    const fallbackContainer = new PIXI.Container();
                    fallbackContainer.name = params.name || "新布局容器";
                    fallbackContainer.x = params.x || 0;
                    fallbackContainer.y = params.y || 0;
                    fallbackContainer.visible = params.visible !== undefined ? params.visible : true;
                    return fallbackContainer;
                }

            default:
                if (DEBUG) log(未知的对象类型: type );
                return null;
        }
    }`;
  }

  /**
   * 生成统一合成代码
   */
  generateUnifiedCode(): string {
    const operations = this.getAllOperations();

    if (operations.length === 0) {
      return '// 没有操作需要生成代码';
    }

    const codeBlocks: string[] = [];

    // 添加头部注释
    codeBlocks.push('// ==================== RPG Editor 生成的修改代码 ====================');
    codeBlocks.push('// 此代码由 RPG Editor 自动生成，请勿手动修改');
    codeBlocks.push('// 生成时间: ' + new Date().toLocaleString());
    codeBlocks.push('');

    // 添加调试标志
    codeBlocks.push('const DEBUG = true;');
    codeBlocks.push('');

    // 添加对象查找方法
    codeBlocks.push(this.generateObjectLookupMethods());

    // 添加对象创建方法
    codeBlocks.push(this.generateObjectCreationMethods());

    // 按场景分组生成代码
    const sceneGroups = this.groupOperationsByScene();

    for (const [sceneName, sceneOperations] of sceneGroups) {
      codeBlocks.push(`// ==================== ${sceneName} 场景修改 ====================`);
      codeBlocks.push('');

      // 生成场景修改代码
      const sceneCode = this.generateSceneModificationCode(sceneName, sceneOperations);
      if (sceneCode) {
        codeBlocks.push(sceneCode);
        codeBlocks.push('');
      }
    }

    // 添加尾部注释
    codeBlocks.push('// ==================== 代码生成完成 ====================');

    return codeBlocks.join('\n');
  }

  /**
   * 生成场景修改代码
   */
  private generateSceneModificationCode(sceneName: string, operations: BaseTempObj[]): string {
    const codeBlocks: string[] = [];

    // 生成场景启动时的修改代码
    codeBlocks.push(`// 修改 ${sceneName} 场景`);
    codeBlocks.push(`const original${sceneName}Start = ${sceneName}.prototype.start;`);
    codeBlocks.push(`${sceneName}.prototype.start = function() {`);
    codeBlocks.push(`    // 调用原始方法`);
    codeBlocks.push(`    original${sceneName}Start.apply(this, arguments);`);
    codeBlocks.push('');
    codeBlocks.push(`    if (DEBUG) console.log('${sceneName}.start 被调用');`);
    codeBlocks.push('');

    // 生成每个操作的代码
    for (const operation of operations) {
      const operationCode = operation.generateCode();
      if (operationCode && operationCode !== '// 没有属性需要修改') {
        // 添加缩进
        const indentedCode = operationCode.split('\n').map(line =>
          line ? `    ${line}` : line
        ).join('\n');

        codeBlocks.push(indentedCode);
        codeBlocks.push('');
      }
    }

    codeBlocks.push('};');

    return codeBlocks.join('\n');
  }

  /**
   * 生成所有操作的代码（旧方法，保持兼容性）
   */
  generateAllCode(): string {
    return this.generateUnifiedCode();
  }

  /**
   * 序列化为JSON
   */
  toJSON(): { [key: string]: any } {
    const result: { [key: string]: any } = {};

    for (const [key, operation] of this.operations) {
      result[key] = operation.toJSON();
    }

    return result;
  }

  /**
   * 从JSON反序列化
   */
  fromJSON(jsonData: { [key: string]: any }): void {
    this.clear();

    for (const [key, data] of Object.entries(jsonData)) {
      const operation = BaseTempObj.fromJSON(data);
      this.operations.set(key, operation);
    }
  }

  /**
   * 生成操作的唯一键
   */
  private generateKey(objectPath: string[], className: string): string {
    return `${className}_${objectPath.join('_')}`;
  }

  /**
   * 获取修改的操作数量
   */
  getModifiedOperationsCount(): number {
    let count = 0;
    for (const operation of this.operations.values()) {
      if (operation.getModifiedProperties().length > 0) {
        count++;
      }
    }
    return count;
  }

  /**
   * 获取所有修改的操作
   */
  getModifiedOperations(): BaseTempObj[] {
    const modified: BaseTempObj[] = [];
    for (const operation of this.operations.values()) {
      if (operation.getModifiedProperties().length > 0) {
        modified.push(operation);
      }
    }
    return modified;
  }

  /**
   * 按场景分组操作
   */
  groupOperationsByScene(): Map<string, BaseTempObj[]> {
    const groups = new Map<string, BaseTempObj[]>();

    for (const operation of this.operations.values()) {
      const objectPath = operation.getObjectPath();
      const sceneName = objectPath.length > 0 ? objectPath[0] : 'Unknown';

      if (!groups.has(sceneName)) {
        groups.set(sceneName, []);
      }

      groups.get(sceneName)!.push(operation);
    }

    return groups;
  }

  /**
   * 生成按场景分组的代码
   */
  generateCodeByScene(): Map<string, string> {
    const sceneGroups = this.groupOperationsByScene();
    const result = new Map<string, string>();

    for (const [sceneName, operations] of sceneGroups) {
      const codeBlocks: string[] = [];

      for (const operation of operations) {
        const code = operation.generateCode();
        if (code && code !== '// 没有属性需要修改') {
          codeBlocks.push(code);
        }
      }

      if (codeBlocks.length > 0) {
        result.set(sceneName, codeBlocks.join('\n\n'));
      }
    }

    return result;
  }

  /**
   * 检查是否有指定对象的操作
   */
  hasOperation(objectPath: string[], className: string): boolean {
    const key = this.generateKey(objectPath, className);
    return this.operations.has(key);
  }

  /**
   * 获取操作的统计信息
   */
  getStatistics(): {
    totalOperations: number;
    modifiedOperations: number;
    sceneCount: number;
    propertyCount: number;
  } {
    const sceneGroups = this.groupOperationsByScene();
    let propertyCount = 0;

    for (const operation of this.operations.values()) {
      propertyCount += operation.getModifiedProperties().length;
    }

    return {
      totalOperations: this.operations.size,
      modifiedOperations: this.getModifiedOperationsCount(),
      sceneCount: sceneGroups.size,
      propertyCount
    };
  }
}
