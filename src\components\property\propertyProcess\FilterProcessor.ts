/**
 * 滤镜组件处理器
 * 处理滤镜相关属性变化并应用到对象上
 */



export interface FilterProperties {
  filters?: any[];
  'filters.add'?: { type: string; params: any };
  'filters.remove'?: { index: number };
  'filters.modify'?: { index: number; params: any };
  'filters.reorder'?: { fromIndex: number; toIndex: number };
}

export interface FilterInfo {
  type: string;
  params: any;
  enabled?: boolean;
}

export class FilterProcessor {
  /**
   * 应用滤镜属性到对象
   */
  static applyProperty(
    object: any,
    propertyName: keyof FilterProperties,
    value: any
  ): void {
    if (!object) {
      console.warn('FilterProcessor: 对象为空');
      return;
    }

    try {
      // 处理特殊滤镜属性
      if (this.handleSpecialFilterProperty(object, propertyName, value)) {
        console.log(`FilterProcessor: 已应用滤镜属性 ${propertyName} 到对象 ${object.constructor?.name}`);
        return;
      }

      // 直接设置对象属性（用于实时预览）
      if (propertyName in object) {
        object[propertyName] = value;
      }

      console.log(`FilterProcessor: 已应用属性 ${propertyName} = ${value} 到对象 ${object.constructor?.name}`);
    } catch (error) {
      console.error(`FilterProcessor: 应用属性 ${propertyName} 失败:`, error);
    }
  }

  /**
   * 处理特殊滤镜属性设置
   */
  static handleSpecialFilterProperty(
    object: any,
    propertyName: keyof FilterProperties,
    value: any
  ): boolean {
    switch (propertyName) {
      case 'filters':
        return this.handleFiltersChange(object, value);

      case 'filters.add':
        return this.handleAddFilter(object, value);

      case 'filters.remove':
        return this.handleRemoveFilter(object, value);

      case 'filters.modify':
        return this.handleModifyFilter(object, value);

      case 'filters.reorder':
        return this.handleReorderFilter(object, value);

      default:
        return false;
    }
  }

  /**
   * 处理滤镜数组变化
   */
  private static handleFiltersChange(object: any, filters: any[]): boolean {
    try {
      console.log('FilterProcessor: 开始处理滤镜数组变化');
      console.log('对象:', object.constructor?.name);
      console.log('输入滤镜数组:', filters);
      console.log('对象当前滤镜:', object.filters);

      // 如果传入的是滤镜实例数组，直接设置
      if (Array.isArray(filters) && filters.length > 0 && filters[0].constructor) {
        console.log('FilterProcessor: 检测到滤镜实例数组，直接设置');
        object.filters = [...filters];
        console.log(`FilterProcessor: 已直接设置 ${filters.length} 个滤镜实例`);
        return true;
      }

      // 确保对象有filters属性
      if (!object.filters) {
        object.filters = [];
      }

      // 清除现有滤镜
      const originalLength = object.filters.length;
      object.filters.length = 0;
      console.log(`FilterProcessor: 清除了 ${originalLength} 个现有滤镜`);

      // 添加新滤镜
      let successCount = 0;
      for (const filterInfo of filters) {
        console.log('FilterProcessor: 处理滤镜信息:', filterInfo);
        const filter = this.createFilter(filterInfo);
        if (filter) {
          object.filters.push(filter);
          successCount++;
          console.log(`FilterProcessor: 成功添加滤镜 ${successCount}:`, filter);
        } else {
          console.warn('FilterProcessor: 滤镜创建失败:', filterInfo);
        }
      }

      console.log(`FilterProcessor: 成功设置 ${successCount}/${filters.length} 个滤镜`);
      console.log('FilterProcessor: 对象最终滤镜数组:', object.filters);

      // 强制触发渲染更新
      if (object.texture && object.texture._updateID !== undefined) {
        object.texture._updateID++;
        console.log('FilterProcessor: 强制纹理更新');
      }

      return true;
    } catch (error) {
      console.error('FilterProcessor: 处理滤镜数组变化失败:', error);
      return false;
    }
  }

  /**
   * 处理添加滤镜
   */
  private static handleAddFilter(object: any, filterInfo: { type: string; params: any }): boolean {
    try {
      // 确保对象有filters属性
      if (!object.filters) {
        object.filters = [];
      }

      const filter = this.createFilter(filterInfo);
      if (filter) {
        object.filters.push(filter);
        console.log(`FilterProcessor: 已添加滤镜 ${filterInfo.type}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error('FilterProcessor: 添加滤镜失败:', error);
      return false;
    }
  }

  /**
   * 处理移除滤镜
   */
  private static handleRemoveFilter(object: any, removeInfo: { index: number }): boolean {
    try {
      if (!object.filters || !Array.isArray(object.filters)) {
        console.warn('FilterProcessor: 对象没有滤镜数组');
        return false;
      }

      if (removeInfo.index >= 0 && removeInfo.index < object.filters.length) {
        object.filters.splice(removeInfo.index, 1);
        console.log(`FilterProcessor: 已移除索引 ${removeInfo.index} 的滤镜`);
        return true;
      } else {
        console.warn(`FilterProcessor: 滤镜索引 ${removeInfo.index} 超出范围`);
        return false;
      }
    } catch (error) {
      console.error('FilterProcessor: 移除滤镜失败:', error);
      return false;
    }
  }

  /**
   * 处理修改滤镜
   */
  private static handleModifyFilter(object: any, modifyInfo: { index: number; params: any }): boolean {
    try {
      if (!object.filters || !Array.isArray(object.filters)) {
        console.warn('FilterProcessor: 对象没有滤镜数组');
        return false;
      }

      if (modifyInfo.index >= 0 && modifyInfo.index < object.filters.length) {
        const filter = object.filters[modifyInfo.index];
        if (filter) {
          // 更新滤镜参数
          Object.assign(filter, modifyInfo.params);
          console.log(`FilterProcessor: 已修改索引 ${modifyInfo.index} 的滤镜参数`);
          return true;
        }
      } else {
        console.warn(`FilterProcessor: 滤镜索引 ${modifyInfo.index} 超出范围`);
        return false;
      }
      return false;
    } catch (error) {
      console.error('FilterProcessor: 修改滤镜失败:', error);
      return false;
    }
  }

  /**
   * 处理重排滤镜
   */
  private static handleReorderFilter(object: any, reorderInfo: { fromIndex: number; toIndex: number }): boolean {
    try {
      if (!object.filters || !Array.isArray(object.filters)) {
        console.warn('FilterProcessor: 对象没有滤镜数组');
        return false;
      }

      const { fromIndex, toIndex } = reorderInfo;
      if (fromIndex >= 0 && fromIndex < object.filters.length &&
        toIndex >= 0 && toIndex < object.filters.length) {

        // 移动滤镜
        const filter = object.filters.splice(fromIndex, 1)[0];
        object.filters.splice(toIndex, 0, filter);

        console.log(`FilterProcessor: 已将滤镜从索引 ${fromIndex} 移动到 ${toIndex}`);
        return true;
      } else {
        console.warn(`FilterProcessor: 滤镜索引超出范围 (${fromIndex} -> ${toIndex})`);
        return false;
      }
    } catch (error) {
      console.error('FilterProcessor: 重排滤镜失败:', error);
      return false;
    }
  }

  /**
   * 创建滤镜实例
   */
  private static createFilter(filterInfo: FilterInfo): any {
    try {
      const { type, params } = filterInfo;
      let filter: any = null;

      console.log(`FilterProcessor: 创建滤镜 ${type}，参数:`, params);

      // 获取全局 PIXI 对象
      const PIXI = (window as any).PIXI;
      if (!PIXI) {
        console.error('FilterProcessor: PIXI 对象不可用');
        return null;
      }

      switch (type) {
        case 'BlurFilter':
          // 尝试不同的 BlurFilter 构造方式
          if (PIXI.filters && PIXI.filters.BlurFilter) {
            filter = new PIXI.filters.BlurFilter(params.blur || 2, params.quality || 4);
          } else if (PIXI.BlurFilter) {
            filter = new PIXI.BlurFilter(params.blur || 2, params.quality || 4);
          } else {
            console.error('FilterProcessor: BlurFilter 不可用');
            return null;
          }
          console.log('FilterProcessor: 创建了 BlurFilter:', filter);
          break;

        case 'ColorMatrixFilter':
          if (PIXI.filters && PIXI.filters.ColorMatrixFilter) {
            filter = new PIXI.filters.ColorMatrixFilter();
          } else if (PIXI.ColorMatrixFilter) {
            filter = new PIXI.ColorMatrixFilter();
          }
          if (filter && params.matrix) {
            filter.matrix = params.matrix;
          }
          break;

        case 'AlphaFilter':
          if (PIXI.filters && PIXI.filters.AlphaFilter) {
            filter = new PIXI.filters.AlphaFilter(params.alpha || 1);
          } else if (PIXI.AlphaFilter) {
            filter = new PIXI.AlphaFilter(params.alpha || 1);
          }
          console.log('FilterProcessor: 创建了 AlphaFilter:', filter);
          break;

        case 'NoiseFilter':
          if (PIXI.filters && PIXI.filters.NoiseFilter) {
            filter = new PIXI.filters.NoiseFilter(params.noise || 0.5, params.seed || Math.random());
          } else if (PIXI.NoiseFilter) {
            filter = new PIXI.NoiseFilter(params.noise || 0.5, params.seed || Math.random());
          }
          break;

        default:
          console.warn(`FilterProcessor: 不支持的滤镜类型 ${type}`);
          return null;
      }

      // 设置通用属性
      if (filter) {
        if (params.enabled !== undefined) {
          filter.enabled = params.enabled;
        }
        console.log(`FilterProcessor: 成功创建滤镜 ${type}:`, filter);
      } else {
        console.error(`FilterProcessor: 创建滤镜 ${type} 失败`);
      }

      return filter;
    } catch (error) {
      console.error(`FilterProcessor: 创建滤镜失败 (${filterInfo.type}):`, error);
      return null;
    }
  }

  /**
   * 获取对象当前的滤镜信息
   */
  static getFiltersInfo(object: any): FilterInfo[] {
    try {
      if (!object.filters || !Array.isArray(object.filters)) {
        return [];
      }

      return object.filters.map((filter: any, index: number) => {
        return {
          type: filter.constructor?.name || 'UnknownFilter',
          params: this.extractFilterParams(filter),
          enabled: filter.enabled !== false
        };
      });
    } catch (error) {
      console.error('FilterProcessor: 获取滤镜信息失败:', error);
      return [];
    }
  }

  /**
   * 提取滤镜参数
   */
  private static extractFilterParams(filter: any): any {
    const params: any = {};

    try {
      // 根据滤镜类型提取相关参数
      const filterType = filter.constructor?.name;

      switch (filterType) {
        case 'BlurFilter':
          params.blur = filter.blur;
          params.quality = filter.quality;
          break;

        case 'ColorMatrixFilter':
          params.matrix = filter.matrix;
          break;

        case 'NoiseFilter':
          params.noise = filter.noise;
          params.seed = filter.seed;
          break;

        case 'AlphaFilter':
          params.alpha = filter.alpha;
          break;

        default:
          // 尝试提取常见属性
          ['alpha', 'blur', 'scale', 'strength', 'threshold'].forEach(prop => {
            if (filter[prop] !== undefined) {
              params[prop] = filter[prop];
            }
          });
      }

      params.enabled = filter.enabled !== false;
    } catch (error) {
      console.error('FilterProcessor: 提取滤镜参数失败:', error);
    }

    return params;
  }

  /**
   * 验证滤镜属性值
   */
  static validateProperty(
    propertyName: keyof FilterProperties,
    value: any
  ): boolean {
    switch (propertyName) {
      case 'filters':
        return Array.isArray(value);

      case 'filters.add':
        return value && typeof value.type === 'string' && value.params;

      case 'filters.remove':
        return value && typeof value.index === 'number' && value.index >= 0;

      case 'filters.modify':
        return value && typeof value.index === 'number' && value.index >= 0 && value.params;

      case 'filters.reorder':
        return value &&
          typeof value.fromIndex === 'number' &&
          typeof value.toIndex === 'number' &&
          value.fromIndex >= 0 && value.toIndex >= 0;

      default:
        return true;
    }
  }
}
