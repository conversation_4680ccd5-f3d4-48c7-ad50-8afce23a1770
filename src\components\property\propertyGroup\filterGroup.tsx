import React, { useState, useEffect, memo } from "react";
import {
  Box, Typography, Button, Menu, ListItemIcon, MenuItem
} from "@mui/material";
import { getObjectPath } from "../../../utils/object/objectPro.ts";
import useProjectStore from "../../../store/Store";
import AddIcon from '@mui/icons-material/Add';
import { registerCustomFilters } from "./filters/CustomFilters.ts";
import { filterOptions, filterParams, FilterOption, FilterParam, AppliedFilter } from "./filters/FilterConfig.ts";
import FilterItem from "./filters/FilterItem.tsx";
// import {
//   recordFilterParamModification,
//   recordAddFilter,
//   recordRemoveFilter
// } from "../../../services/backendServer/filter";

// 注册自定义滤镜
registerCustomFilters();

interface FilterGroupProps {
  selectedObject: any;
  forceUpdate: () => void;
}

const FilterGroup: React.FC<FilterGroupProps> = memo(({ selectedObject, forceUpdate }) => {
  // 获取Store方法
  const setSelectedObjectsProperty = useProjectStore(state => state.setSelectedObjectsProperty);

  // 当前应用的滤镜列表
  const [appliedFilters, setAppliedFilters] = useState<AppliedFilter[]>([]);
  // 当前选中的滤镜索引
  const [selectedFilterIndex, setSelectedFilterIndex] = useState<number>(-1);
  // 滤镜选择菜单状态
  const [filterMenuOpen, setFilterMenuOpen] = useState<boolean>(false);
  // 滤镜菜单锚点
  const [filterMenuAnchor, setFilterMenuAnchor] = useState<null | HTMLElement>(null);

  // 当选中对象变化时，获取其滤镜
  useEffect(() => {
    if (selectedObject) {
      loadFilters();
    } else {
      setAppliedFilters([]);
      setSelectedFilterIndex(-1);
    }
  }, [selectedObject]);

  // 加载对象的滤镜
  const loadFilters = () => {
    try {
      if (selectedObject) {
        const filters: AppliedFilter[] = [];

        // 确保对象有filters属性且是数组
        const objectFilters = selectedObject.filters || [];
        if (!Array.isArray(objectFilters)) {
          console.log('对象的filters属性不是数组');
          setAppliedFilters([]);
          setSelectedFilterIndex(-1);
          return;
        }

        // 处理滤镜，跳过ColorFilter（由SpriteColorGroup处理）
        objectFilters.forEach((filter: any, index: number) => {
          // 跳过null或undefined的滤镜
          if (!filter) return;

          // 跳过ColorFilter，由SpriteColorGroup处理
          if (filter.constructor && filter.constructor.name === 'ColorFilter') {
            return;
          }

          // 尝试识别滤镜类型
          const filterType = identifyFilterType(filter);
          if (filterType) {
            const params = extractFilterParams(filter, filterType);

            filters.push({
              id: `filter_${index}`,
              type: filterType,
              params: params,
              instance: filter
            });
          }
        });

        // 限制滤镜数量
        const limitedFilters = filters.length > 10 ? filters.slice(0, 10) : filters;

        setAppliedFilters(limitedFilters);
        setSelectedFilterIndex(limitedFilters.length > 0 ? 0 : -1);

        console.log('加载了', limitedFilters.length, '个滤镜');
      }
    } catch (error) {
      console.error('加载滤镜时出错:', error);
      // 出错时设置为空数组
      setAppliedFilters([]);
      setSelectedFilterIndex(-1);
    }
  };

  // 识别滤镜类型
  const identifyFilterType = (filter: any): string => {
    try {
      if (!filter || !filter.constructor) {
        return '';
      }

      // 检查是否是BlurFilter
      if (filter.constructor.name === 'BlurFilter') {
        return 'blur';
      }

      // 检查是否是AlphaFilter
      if (filter.constructor.name === 'AlphaFilter') {
        return 'alpha';
      }

      // 检查自定义滤镜类型
      if (filter.constructor.name === 'SmokeFilter') {
        return 'smoke';
      }

      if (filter.constructor.name === 'FireFilter') {
        return 'fire';
      }

      if (filter.constructor.name === 'WaterFilter') {
        return 'water';
      }

      if (filter.constructor.name === 'ClothFilter') {
        return 'cloth';
      }

      if (filter.constructor.name === 'GlowFilter') {
        return 'glow';
      }

      if (filter.constructor.name === 'FrostFilter') {
        return 'frost';
      }

      if (filter.constructor.name === 'AdvancedColorFilter') {
        return 'advancedColor';
      }

      // 尝试通过属性识别滤镜类型
      if (filter.blur !== undefined) {
        return 'blur';
      } else if (filter.alpha !== undefined) {
        return 'alpha';
      } else if (filter.uniforms) {
        if (filter.uniforms.intensity !== undefined) {
          // 根据其他特定属性区分不同的自定义滤镜
          if (filter.uniforms.fireColor !== undefined) {
            return 'fire';
          } else if (filter.uniforms.glowColor !== undefined) {
            return 'glow';
          } else if (filter.uniforms.time !== undefined) {
            // 多个滤镜都有time和intensity，需要进一步区分
            if (filter.uniforms.amplitude !== undefined) {
              return 'water';
            } else if (filter.uniforms.waveFrequency !== undefined) {
              return 'cloth';
            } else {
              // 默认为烟雾滤镜
              return 'smoke';
            }
          }
        }
      }

      console.log('未能识别滤镜类型:', filter);
      return '';
    } catch (error) {
      console.error('识别滤镜类型时出错:', error);
      return '';
    }
  };

  // 提取滤镜参数
  const extractFilterParams = (filter: any, filterType: string): Record<string, any> => {
    const params: Record<string, any> = {};
    const paramDefs = filterParams[filterType] || [];

    // 处理自定义滤镜
    if (filter.uniforms) {
      // 处理基本参数
      paramDefs.forEach(param => {
        // 处理颜色数组参数
        if (param.name.startsWith('fireColor_') && filter.uniforms.fireColor) {
          const colorIndex = param.name.endsWith('_r') ? 0 : param.name.endsWith('_g') ? 1 : 2;
          params[param.name] = filter.uniforms.fireColor[colorIndex] !== undefined ?
            filter.uniforms.fireColor[colorIndex] : param.defaultValue;
        }
        else if (param.name.startsWith('glowColor_') && filter.uniforms.glowColor) {
          const colorIndex = param.name.endsWith('_r') ? 0 : param.name.endsWith('_g') ? 1 : 2;
          params[param.name] = filter.uniforms.glowColor[colorIndex] !== undefined ?
            filter.uniforms.glowColor[colorIndex] : param.defaultValue;
        }
        // 处理普通参数
        else if (filter.uniforms[param.name] !== undefined) {
          params[param.name] = filter.uniforms[param.name];
        } else {
          params[param.name] = param.defaultValue;
        }
      });
    } else {
      // 处理其他滤镜
      paramDefs.forEach(param => {
        if (filter[param.name] !== undefined) {
          params[param.name] = filter[param.name];
        } else if (filter.uniforms && filter.uniforms[param.name] !== undefined) {
          params[param.name] = filter.uniforms[param.name];
        } else {
          params[param.name] = param.defaultValue;
        }
      });
    }

    return params;
  };

  // 打开滤镜选择菜单
  const handleOpenFilterMenu = (event: React.MouseEvent<HTMLElement>) => {
    setFilterMenuAnchor(event.currentTarget);
    setFilterMenuOpen(true);
  };

  // 关闭滤镜选择菜单
  const handleCloseFilterMenu = () => {
    setFilterMenuAnchor(null);
    setFilterMenuOpen(false);
  };

  // 添加滤镜
  const handleAddFilter = async (filterId: string) => {
    handleCloseFilterMenu();

    const filterOption = filterOptions.find(option => option.id === filterId);
    if (!filterOption) return;

    try {
      // 检查是否已经有相同类型的滤镜
      const existingFilterIndex = appliedFilters.findIndex(filter => filter.type === filterId);

      if (existingFilterIndex >= 0) {
        // 如果已经有相同类型的滤镜，选中它
        setSelectedFilterIndex(existingFilterIndex);
        return;
      }

      // 限制滤镜数量，最多10个
      if (appliedFilters.length >= 10) {
        alert('滤镜数量已达到上限（10个），请先删除一些滤镜再添加新的。');
        return;
      }

      // 创建新的滤镜
      const newFilter: AppliedFilter = {
        id: `filter_${Date.now()}`,
        type: filterId,
        params: { ...filterOption.defaultParams }
      };

      // 添加到应用的滤镜列表
      const newFilters = [...appliedFilters, newFilter];
      setAppliedFilters(newFilters);
      setSelectedFilterIndex(newFilters.length - 1);

      // 应用到对象
      await applyFiltersToObject(newFilters);

      // 记录添加滤镜操作到后端
      // try {
      //   await recordAddFilter(
      //     selectedObject,
      //     filterId,
      //     filterOption.defaultParams,
      //     newFilters.length - 1 // 插入位置
      //   );
      //   console.log('成功记录添加滤镜操作到后端');
      // } catch (error) {
      //   console.error('记录添加滤镜操作到后端失败:', error);
      // }
    } catch (error) {
      console.error('添加滤镜时出错:', error);
    }
  };

  // 删除滤镜
  const handleDeleteFilter = async (index: number) => {
    try {
      // 记录删除操作到后端（在实际删除之前）
      // try {
      //   await recordRemoveFilter(selectedObject, index);
      //   console.log('成功记录删除滤镜操作到后端');
      // } catch (error) {
      //   console.error('记录删除滤镜操作到后端失败:', error);
      // }

      const newFilters = [...appliedFilters];
      newFilters.splice(index, 1);
      setAppliedFilters(newFilters);

      if (selectedFilterIndex === index) {
        setSelectedFilterIndex(newFilters.length > 0 ? 0 : -1);
      } else if (selectedFilterIndex > index) {
        setSelectedFilterIndex(selectedFilterIndex - 1);
      }

      // 应用到对象
      await applyFiltersToObject(newFilters);
    } catch (error) {
      console.error('删除滤镜时出错:', error);
    }
  };

  // 更新滤镜参数
  const handleUpdateFilterParam = async (paramName: string, value: any) => {
    if (selectedFilterIndex < 0 || selectedFilterIndex >= appliedFilters.length) return;

    try {
      const newFilters = [...appliedFilters];
      const filter = newFilters[selectedFilterIndex];

      // 保存旧值用于后端记录
      const oldValue = filter.params[paramName];

      // 更新参数
      filter.params = {
        ...filter.params,
        [paramName]: value
      };

      // 如果有实例，直接更新实例参数
      if (filter.instance) {
        // 使用通用的参数更新方法
        updateFilterInstanceParams(filter.instance, filter.type, filter.params);

        // 更新状态
        setAppliedFilters(newFilters);

        // 强制重新渲染
        forceUpdate();

        // 记录参数修改到后端
        // try {
        //   await recordFilterParamModification(
        //     selectedObject,
        //     selectedFilterIndex,
        //     paramName,
        //     value
        //   );
        //   console.log('成功记录滤镜参数修改到后端');
        // } catch (error) {
        //   console.error('记录滤镜参数修改到后端失败:', error);
        // }

        // 触发属性变更事件
        if (selectedObject) {
          const objectPath = getObjectPath(selectedObject);
          const event = new CustomEvent('property-changed', {
            detail: {
              objectPath: objectPath,
              propertyName: 'filters',
              oldValue: selectedObject.filters,
              value: selectedObject.filters
            }
          });
          window.dispatchEvent(event);
        }
      } else {
        // 如果没有实例，需要重新应用滤镜
        setAppliedFilters(newFilters);
        await applyFiltersToObject(newFilters);
      }
    } catch (error) {
      console.error(`更新滤镜参数 ${paramName} 时出错:`, error);
    }
  };

  // 将滤镜应用到对象
  const applyFiltersToObject = async (filters: AppliedFilter[]) => {
    if (!selectedObject) return;

    try {
      // 保存原始滤镜列表，用于历史记录
      const originalFilters = selectedObject.filters ? [...selectedObject.filters] : [];

      // 创建新的滤镜实例数组
      const newFilterInstances: any[] = [];

      // 检查是否已经有太多滤镜，如果超过10个，则只保留最新的10个
      const filtersToApply = filters.length > 10 ? filters.slice(-10) : filters;

      // 保留ColorFilter（如果存在）
      if (originalFilters.length > 0) {
        let colorFilterFound = false;
        originalFilters.forEach(filter => {
          if (filter && filter.constructor && filter.constructor.name === 'ColorFilter') {
            if (!colorFilterFound) {
              newFilterInstances.push(filter);
              colorFilterFound = true;
            }
          }
        });
      }

      // 创建并添加新的滤镜实例
      filtersToApply.forEach(filter => {
        const filterOption = filterOptions.find(option => option.id === filter.type);
        if (!filterOption) return;

        // 如果已经有实例且类型相同，尝试更新参数而不是创建新实例
        if (filter.instance &&
          filter.instance.constructor &&
          filter.instance.constructor.name === filterOption.filterClass.split('.').pop()) {

          // 更新现有实例的参数
          updateFilterInstanceParams(filter.instance, filter.type, filter.params);
          newFilterInstances.push(filter.instance);
        } else {
          // 创建新的滤镜实例
          const filterInstance = createFilterInstance(filterOption.filterClass, filter.params);
          if (filterInstance) {
            newFilterInstances.push(filterInstance);
            // 更新滤镜实例引用
            filter.instance = filterInstance;
          }
        }
      });

      // 应用滤镜到对象
      try {
        // 直接赋值
        selectedObject.filters = newFilterInstances;

        // 使用Store中的统一方法更新属性
        setSelectedObjectsProperty('filters', newFilterInstances);
      } catch (e) {
        console.error('应用滤镜失败', e);
      }

      // 记录历史
      const objectPath = getObjectPath(selectedObject);
      const event = new CustomEvent('property-changed', {
        detail: {
          objectPath: objectPath,
          propertyName: 'filters',
          oldValue: originalFilters,
          value: newFilterInstances
        }
      });
      window.dispatchEvent(event);

      // 强制重新渲染
      forceUpdate();

      console.log('成功应用滤镜到对象，滤镜数量:', newFilterInstances.length);
    } catch (error) {
      console.error('应用滤镜时出错:', error);
    }
  };

  // 更新滤镜实例参数
  const updateFilterInstanceParams = (instance: any, filterType: string, params: Record<string, any>) => {
    try {
      console.log('updateFilterInstanceParams 被调用:', {
        instance: instance,
        filterType: filterType,
        params: params,
        constructorName: instance?.constructor?.name,
        hasUniforms: !!instance?.uniforms
      });
      // 特殊处理 BlurFilter（即使它有 uniforms）
      if (instance.constructor && instance.constructor.name === 'BlurFilter') {
        console.log('处理BlurFilter，参数:', params);
        console.log('BlurFilter实例属性:', Object.keys(instance));

        if (params.blur !== undefined) {
          // PIXI.BlurFilter 的模糊值属性可能是 blur 或 blurX/blurY
          if (instance.blur !== undefined) {
            instance.blur = params.blur;
            console.log('设置instance.blur =', params.blur);
          } else if (instance.blurX !== undefined && instance.blurY !== undefined) {
            instance.blurX = params.blur;
            instance.blurY = params.blur;
            console.log('设置instance.blurX/blurY =', params.blur);
          } else if (instance.uniforms && instance.uniforms.uBlur !== undefined) {
            instance.uniforms.uBlur = params.blur;
            console.log('设置instance.uniforms.uBlur =', params.blur);
          }
        }
        if (params.quality !== undefined) {
          if (instance.quality !== undefined) {
            instance.quality = params.quality;
            console.log('设置instance.quality =', params.quality);
          } else if (instance.uniforms && instance.uniforms.uQuality !== undefined) {
            instance.uniforms.uQuality = params.quality;
            console.log('设置instance.uniforms.uQuality =', params.quality);
          }
        }
      }
      // 处理自定义滤镜
      else if (instance.uniforms) {
        // 处理颜色数组参数
        if (params.fireColor_r !== undefined || params.fireColor_g !== undefined || params.fireColor_b !== undefined) {
          instance.uniforms.fireColor = [
            params.fireColor_r !== undefined ? params.fireColor_r : instance.uniforms.fireColor[0],
            params.fireColor_g !== undefined ? params.fireColor_g : instance.uniforms.fireColor[1],
            params.fireColor_b !== undefined ? params.fireColor_b : instance.uniforms.fireColor[2]
          ];
        }

        if (params.glowColor_r !== undefined || params.glowColor_g !== undefined || params.glowColor_b !== undefined) {
          instance.uniforms.glowColor = [
            params.glowColor_r !== undefined ? params.glowColor_r : instance.uniforms.glowColor[0],
            params.glowColor_g !== undefined ? params.glowColor_g : instance.uniforms.glowColor[1],
            params.glowColor_b !== undefined ? params.glowColor_b : instance.uniforms.glowColor[2]
          ];
        }

        // 更新其他参数
        for (const [key, value] of Object.entries(params)) {
          // 跳过颜色数组的分量参数，因为已经在上面处理过了
          if (key.startsWith('fireColor_') || key.startsWith('glowColor_')) {
            continue;
          }

          if (instance.uniforms[key] !== undefined) {
            instance.uniforms[key] = value;
          }
        }
      }
      // 处理标准PIXI滤镜（没有 uniforms 的）
      else {
        // 特殊处理 AlphaFilter
        if (instance.constructor && instance.constructor.name === 'AlphaFilter') {
          if (params.alpha !== undefined) {
            instance.alpha = params.alpha;
          }
        }
        // 处理其他标准滤镜
        else {
          for (const [key, value] of Object.entries(params)) {
            if (instance[key] !== undefined) {
              instance[key] = value;
            }
          }
        }
      }
    } catch (error) {
      console.error('更新滤镜实例参数时出错:', error);
    }
  };

  // 创建滤镜实例
  const createFilterInstance = (filterClass: string, params: Record<string, any>): any => {
    try {
      // 直接从全局window获取PIXI
      if (!(window as any).PIXI) {
        console.error('无法获取PIXI');
        return null;
      }

      // 解析滤镜类路径
      const parts = filterClass.split('.');
      let FilterConstructor: any = window;

      for (const part of parts) {
        FilterConstructor = FilterConstructor[part];
      }

      if (typeof FilterConstructor !== 'function') {
        console.error(`滤镜类 ${filterClass} 不是一个构造函数`);
        return null;
      }

      // 创建滤镜实例
      let filterInstance;

      // 根据滤镜类型创建实例
      if (filterClass === 'PIXI.filters.AlphaFilter') {
        // AlphaFilter可以接收alpha参数
        filterInstance = new FilterConstructor(params.alpha);
      } else if (filterClass === 'PIXI.filters.BlurFilter') {
        // BlurFilter可以接收blur和quality参数
        filterInstance = new FilterConstructor(params.blur, params.quality);
      } else {
        // 其他滤镜直接创建
        filterInstance = new FilterConstructor();

        // 设置参数
        for (const [key, value] of Object.entries(params)) {
          if (key === 'color' && typeof value === 'string') {
            try {
              // 将颜色字符串转换为十六进制数字
              const colorValue = parseInt(value.replace('#', '0x'), 16);
              filterInstance[key] = colorValue;
            } catch (e) {
              console.error(`转换颜色值失败: ${value}`, e);
            }
          } else {
            filterInstance[key] = value;
          }
        }
      }

      return filterInstance;
    } catch (error) {
      console.error(`创建滤镜实例 ${filterClass} 时出错:`, error);
      return null;
    }
  };

  // 如果对象不存在，不显示此组件
  if (!selectedObject) {
    return null;
  }

  // 检查对象是否支持滤镜
  const supportsFilters = selectedObject.filters !== undefined ||
    (typeof selectedObject === 'object' &&
      selectedObject !== null &&
      'filters' in selectedObject);

  if (!supportsFilters) {
    return null;
  }

  // 检查对象是否已经有滤镜或者是否是可以添加滤镜的对象类型
  const hasFilters = selectedObject.filters && selectedObject.filters.length > 0;
  const isSprite = selectedObject.constructor &&
    (selectedObject.constructor.name === 'Sprite' ||
      selectedObject.constructor.name === 'Sprite_Base' ||
      selectedObject.constructor.name === 'Sprite_Picture' ||
      selectedObject.constructor.name === 'Sprite_Character' ||
      selectedObject.constructor.name === 'Sprite_Animation' ||
      selectedObject.constructor.name === 'Window_Base' ||
      selectedObject.constructor.name === 'TilingSprite');

  // 只有当对象已经有滤镜或者是可以添加滤镜的对象类型时才显示滤镜组件
  if (!hasFilters && !isSprite) {
    return null;
  }

  return (
    <Box sx={{ mt: 2 }}>
      <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
        滤镜效果
      </Typography>

      {/* 滤镜列表 */}
      <Box sx={{ mb: 1 }}>
        {appliedFilters.map((filter, index) => {
          const filterOption = filterOptions.find(option => option.id === filter.type);
          const paramDefs = filterParams[filter.type] || [];

          return filterOption ? (
            <FilterItem
              key={filter.id}
              filter={filter}
              filterOption={filterOption}
              paramDefs={paramDefs}
              isSelected={index === selectedFilterIndex}
              onSelect={() => setSelectedFilterIndex(index)}
              onDelete={() => handleDeleteFilter(index)}
              onUpdateParam={handleUpdateFilterParam}
            />
          ) : null;
        })}

        {/* 添加滤镜按钮 */}
        <Button
          variant="outlined"
          size="small"
          startIcon={<AddIcon />}
          onClick={handleOpenFilterMenu}
          sx={{ mt: 1, width: '100%' }}
        >
          添加滤镜
        </Button>

        {/* 滤镜选择菜单 */}
        <Menu
          anchorEl={filterMenuAnchor}
          open={filterMenuOpen}
          onClose={handleCloseFilterMenu}
        >
          {filterOptions.map((option) => (
            <MenuItem
              key={option.id}
              onClick={() => handleAddFilter(option.id)}
              sx={{ minWidth: '150px' }}
            >
              <ListItemIcon>{option.icon}</ListItemIcon>
              {option.name}
            </MenuItem>
          ))}
        </Menu>
      </Box>
    </Box>
  );
}, (prevProps, nextProps) => {
  // 只有当selectedObject发生变化时才重新渲染
  return prevProps.selectedObject === nextProps.selectedObject;
});

export default FilterGroup;
