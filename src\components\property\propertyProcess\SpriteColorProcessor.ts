/**
 * Sprite颜色组件处理器
 * 处理Sprite颜色相关属性变化并应用到对象上
 */

import { setObjectProperty } from '../../../services';

export interface SpriteColorProperties {
  hue?: number;
  colorTone?: [number, number, number, number]; // [red, green, blue, gray]
  blendColor?: [number, number, number, number]; // [red, green, blue, alpha]
  tint?: number;
  alpha?: number;
}

export class SpriteColorProcessor {
  /**
   * 应用颜色属性到Sprite对象
   */
  static applyProperty(
    object: any,
    propertyName: keyof SpriteColorProperties,
    value: any
  ): void {
    if (!object) {
      console.warn('SpriteColorProcessor: 对象为空');
      return;
    }

    // 检查是否是Sprite对象
    if (!this.isSpriteObject(object)) {
      console.warn('SpriteColorProcessor: 对象不是Sprite类型');
      return;
    }

    try {
      // 获取对象路径和类名
      const objectPath = object._rpgEditorPath || ['Unknown'];
      const className = object.constructor?.name || 'Unknown';

      // 处理特殊颜色属性
      if (this.handleSpecialColorProperty(object, propertyName, value)) {
        // 记录到generators系统
        setObjectProperty(objectPath, className, propertyName, value);
        console.log(`SpriteColorProcessor: 已应用颜色属性 ${propertyName} 到Sprite`, objectPath);
        return;
      }

      // 直接设置对象属性（用于实时预览）
      if (propertyName in object) {
        object[propertyName] = value;
      }

      // 记录到generators系统
      setObjectProperty(objectPath, className, propertyName, value);

      console.log(`SpriteColorProcessor: 已应用属性 ${propertyName} = ${value} 到Sprite`, objectPath);
    } catch (error) {
      console.error(`SpriteColorProcessor: 应用属性 ${propertyName} 失败:`, error);
    }
  }

  /**
   * 批量应用颜色属性
   */
  static applyProperties(
    object: any,
    properties: Partial<SpriteColorProperties>
  ): void {
    if (!object) {
      console.warn('SpriteColorProcessor: 对象为空');
      return;
    }

    if (!this.isSpriteObject(object)) {
      console.warn('SpriteColorProcessor: 对象不是Sprite类型');
      return;
    }

    for (const [propertyName, value] of Object.entries(properties)) {
      if (value !== undefined && value !== null) {
        this.applyProperty(object, propertyName as keyof SpriteColorProperties, value);
      }
    }
  }

  /**
   * 检查是否是Sprite对象
   */
  private static isSpriteObject(object: any): boolean {
    return object && (
      object.constructor?.name === 'Sprite' ||
      object instanceof (window as any).Sprite ||
      typeof object.setHue === 'function' ||
      typeof object.setColorTone === 'function' ||
      typeof object.setBlendColor === 'function'
    );
  }

  /**
   * 处理特殊颜色属性设置
   */
  static handleSpecialColorProperty(
    object: any,
    propertyName: keyof SpriteColorProperties,
    value: any
  ): boolean {
    switch (propertyName) {
      case 'hue':
        return this.handleHueChange(object, value);

      case 'colorTone':
        return this.handleColorToneChange(object, value);

      case 'blendColor':
        return this.handleBlendColorChange(object, value);

      case 'tint':
        return this.handleTintChange(object, value);

      default:
        return false;
    }
  }

  /**
   * 处理色相变化
   */
  private static handleHueChange(object: any, hue: number): boolean {
    try {
      if (typeof object.setHue === 'function') {
        object.setHue(hue);
        return true;
      } else if (object._hue !== undefined) {
        object._hue = hue;
        // 如果有更新方法，调用它
        if (typeof object.updateColorFilter === 'function') {
          object.updateColorFilter();
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('SpriteColorProcessor: 处理色相变化失败:', error);
      return false;
    }
  }

  /**
   * 处理色调变化
   */
  private static handleColorToneChange(object: any, colorTone: [number, number, number, number]): boolean {
    try {
      if (typeof object.setColorTone === 'function') {
        object.setColorTone(colorTone);
        return true;
      } else if (object._colorTone !== undefined) {
        object._colorTone = [...colorTone];
        // 如果有更新方法，调用它
        if (typeof object.updateColorFilter === 'function') {
          object.updateColorFilter();
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('SpriteColorProcessor: 处理色调变化失败:', error);
      return false;
    }
  }

  /**
   * 处理混合颜色变化
   */
  private static handleBlendColorChange(object: any, blendColor: [number, number, number, number]): boolean {
    try {
      if (typeof object.setBlendColor === 'function') {
        object.setBlendColor(blendColor);
        return true;
      } else if (object._blendColor !== undefined) {
        object._blendColor = [...blendColor];
        // 如果有更新方法，调用它
        if (typeof object.updateColorFilter === 'function') {
          object.updateColorFilter();
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('SpriteColorProcessor: 处理混合颜色变化失败:', error);
      return false;
    }
  }

  /**
   * 处理色调变化（PIXI风格）
   */
  private static handleTintChange(object: any, tint: number): boolean {
    try {
      if (object.tint !== undefined) {
        object.tint = tint;
        return true;
      }
      return false;
    } catch (error) {
      console.error('SpriteColorProcessor: 处理tint变化失败:', error);
      return false;
    }
  }

  /**
   * 验证颜色属性值
   */
  static validateProperty(
    propertyName: keyof SpriteColorProperties,
    value: any
  ): boolean {
    switch (propertyName) {
      case 'hue':
        return typeof value === 'number' && value >= 0 && value <= 360;

      case 'colorTone':
      case 'blendColor':
        return Array.isArray(value) &&
          value.length === 4 &&
          value.every(v => typeof v === 'number' && v >= -255 && v <= 255);

      case 'tint':
        return typeof value === 'number' && value >= 0 && value <= 0xFFFFFF;

      case 'alpha':
        return typeof value === 'number' && value >= 0 && value <= 1;

      default:
        return true;
    }
  }

  /**
   * 获取颜色属性的默认值
   */
  static getDefaultValue(propertyName: keyof SpriteColorProperties): any {
    switch (propertyName) {
      case 'hue':
        return 0;
      case 'colorTone':
        return [0, 0, 0, 0];
      case 'blendColor':
        return [0, 0, 0, 0];
      case 'tint':
        return 0xFFFFFF;
      case 'alpha':
        return 1;
      default:
        return null;
    }
  }

  /**
   * 将RGB值转换为十六进制
   */
  static rgbToHex(r: number, g: number, b: number): number {
    return ((r & 0xFF) << 16) | ((g & 0xFF) << 8) | (b & 0xFF);
  }

  /**
   * 将十六进制值转换为RGB
   */
  static hexToRgb(hex: number): { r: number; g: number; b: number } {
    return {
      r: (hex >> 16) & 0xFF,
      g: (hex >> 8) & 0xFF,
      b: hex & 0xFF
    };
  }
}
