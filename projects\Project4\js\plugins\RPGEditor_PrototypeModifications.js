(function() {
"use strict";
// ==================== RPG Editor 生成的修改代码 ====================
// 此代码由 RPG Editor 自动生成，请勿手动修改
// 生成时间: 2025/5/29 12:56:44



// ==================== 对象查找方法 ====================
    // 获取插件参数
    const parameters = PluginManager.parameters("RPGEditor_PrototypeModifications");
    const DEBUG = parameters["debug"] === "true";

    // 调试日志函数
    function log(message, ...args) {
        if (DEBUG) {
            console.log("[RPGEditor]", message, ...args);
        }
    }
    // 工具函数：根据场景路径查找对象
    function findObjectByScenePath(scenePath) {
        if (!scenePath || scenePath.length === 0) return null;

        let current = SceneManager._scene;
        if (!current) return null;

        // 检查第一个元素是否为创建操作标记
        const firstElement = scenePath[0];
        let isCreationOperation = firstElement === "+";

        // 确定实际的场景路径起始位置
        let startIndex = isCreationOperation ? 1 : 0;

        // 验证场景名称
        const expectedSceneName = scenePath[startIndex];
        const actualSceneName = current.constructor.name;
        if (actualSceneName !== expectedSceneName) {
            if (DEBUG) console.log(`[Scene Mismatch] Expected: ${expectedSceneName}, Actual: ${actualSceneName}`);
            return null;
        }

        // 确定遍历的结束位置
        let endIndex = scenePath.length;
        if (isCreationOperation) {
            // 创建操作：最后一个索引不查找，那是要创建的位置
            endIndex = scenePath.length - 1;
            if (DEBUG) console.log(`[Creation Operation] Finding parent object, skipping last index: ${scenePath[scenePath.length - 1]}`);
        }

        // 遍历路径
        for (let i = startIndex + 1; i < endIndex; i++) {
            const index = parseInt(scenePath[i]);
            if (isNaN(index) || !current.children || !current.children[index]) {
                if (DEBUG) console.log(`[Path Break] Index ${index} does not exist in ${current.constructor.name}`);
                return null;
            }
            current = current.children[index];
        }

        return current;
    }


    // 工具函数：创建游戏对象
    function createGameObject(type, params = {}) {
        if (DEBUG) log(`[GameObject] Creating new object: ${type}`, params);

        switch (type) {
            case "Sprite":
                const sprite = new Sprite();
                sprite.name = params.name || "NewSprite";
                sprite.x = params.x || 0;
                sprite.y = params.y || 0;
                sprite.visible = params.visible !== undefined ? params.visible : true;
                return sprite;

            case "Label":
                const label = new Sprite();
                label.name = params.name || "NewLabel";
                label.x = params.x || 0;
                label.y = params.y || 0;
                label.visible = params.visible !== undefined ? params.visible : true;

                const bitmap = new Bitmap(200, 40);
                bitmap.fontSize = 20;
                bitmap.textColor = "#ffffff";
                bitmap.outlineColor = "rgba(0, 0, 0, 0.5)";
                bitmap.outlineWidth = 4;
                bitmap.drawText(params.text || "New Text", 0, 0, 200, 40, "left");
                bitmap.text = params.text || "New Text";

                label.bitmap = bitmap;
                return label;

            case "Container":
                const container = new PIXI.Container();
                container.name = params.name || "NewContainer";
                container.x = params.x || 0;
                container.y = params.y || 0;
                container.visible = params.visible !== undefined ? params.visible : true;
                return container;

            case "Window":
                const rect = new Rectangle(0, 0, 200, 100);
                const windowObj = new Window_Base(rect);
                windowObj.name = params.name || "NewWindow";
                windowObj.x = params.x || 0;
                windowObj.y = params.y || 0;
                windowObj.visible = params.visible !== undefined ? params.visible : true;
                return windowObj;

            case "Button":
                const button = new Sprite_Clickable();
                button.name = params.name || "NewButton";
                button.x = params.x || 0;
                button.y = params.y || 0;
                button.visible = params.visible !== undefined ? params.visible : true;

                const buttonBitmap = new Bitmap(120, 40);
                buttonBitmap.fillRect(0, 0, 120, 40, "#3498db");
                buttonBitmap.fontSize = 18;
                buttonBitmap.textColor = "#ffffff";
                buttonBitmap.drawText(params.text || "Button", 0, 0, 120, 40, "center");

                button.bitmap = buttonBitmap;
                button._isButton = true;
                return button;

            case "LayoutContainer":
                if (typeof LayoutContainer !== "undefined") {
                    const layoutContainer = new LayoutContainer();
                    layoutContainer.name = params.name || "NewLayoutContainer";
                    layoutContainer.x = params.x || 0;
                    layoutContainer.y = params.y || 0;
                    layoutContainer.visible = params.visible !== undefined ? params.visible : true;
                    return layoutContainer;
                } else {
                    // 如果没有LayoutContainer类，创建一个普通的Container
                    const fallbackContainer = new PIXI.Container();
                    fallbackContainer.name = params.name || "NewLayoutContainer";
                    fallbackContainer.x = params.x || 0;
                    fallbackContainer.y = params.y || 0;
                    fallbackContainer.visible = params.visible !== undefined ? params.visible : true;
                    return fallbackContainer;
                }

            default:
                if (DEBUG) log(`[GameObject] Unknown object type: ${type}`);
                return null;
        }
    }
// ==================== Scene_Title 场景修改 ====================

// 修改 Scene_Title 场景
const originalScene_TitleStart = Scene_Title.prototype.start;
Scene_Title.prototype.start = function() {
    // 调用原始方法
    originalScene_TitleStart.apply(this, arguments);

    if (DEBUG) console.log('Scene_Title.start 被调用');

    // 设置 Sprite 的 6 个属性
    const target_sprite_Scene_Title_2Path = ["Scene_Title","2"];
    const target_sprite_Scene_Title_2 = findObjectByScenePath(target_sprite_Scene_Title_2Path);
    if (target_sprite_Scene_Title_2) {
        target_sprite_Scene_Title_2.x = 39;
        if (DEBUG) console.log('设置属性:', 'x', 39, target_sprite_Scene_Title_2);
        target_sprite_Scene_Title_2.skew.x = 2;
        if (DEBUG) console.log('设置属性:', 'skew.x', 2, target_sprite_Scene_Title_2);
        target_sprite_Scene_Title_2.rotation = 0;
        if (DEBUG) console.log('设置属性:', 'rotation', 0, target_sprite_Scene_Title_2);
        target_sprite_Scene_Title_2.skew.y = 0.1;
        if (DEBUG) console.log('设置属性:', 'skew.y', 0.1, target_sprite_Scene_Title_2);
        target_sprite_Scene_Title_2.alpha = 0.5;
        if (DEBUG) console.log('设置属性:', 'alpha', 0.5, target_sprite_Scene_Title_2);
        target_sprite_Scene_Title_2.y = 51;
        if (DEBUG) console.log('设置属性:', 'y', 51, target_sprite_Scene_Title_2);
    }

};

// ==================== 代码生成完成 ====================
})();