D:\gpgmake\rpgEditor\src-tauri\target\debug\rpgeditor.exe: D:\gpgmake\rpgEditor\src-tauri\build.rs D:\gpgmake\rpgEditor\src-tauri\capabilities D:\gpgmake\rpgEditor\src-tauri\src\asset_manager.rs D:\gpgmake\rpgEditor\src-tauri\src\coreRPGtype\mod.rs D:\gpgmake\rpgEditor\src-tauri\src\coreRPGtype\readJs.rs D:\gpgmake\rpgEditor\src-tauri\src\coreRPGtype\type_reader.rs D:\gpgmake\rpgEditor\src-tauri\src\file_utils.rs D:\gpgmake\rpgEditor\src-tauri\src\history.rs D:\gpgmake\rpgEditor\src-tauri\src\lib.rs D:\gpgmake\rpgEditor\src-tauri\src\main.rs D:\gpgmake\rpgEditor\src-tauri\src\project.rs D:\gpgmake\rpgEditor\src-tauri\src\save\api\entry.rs D:\gpgmake\rpgEditor\src-tauri\src\save\api\mod.rs D:\gpgmake\rpgEditor\src-tauri\src\save\mod.rs D:\gpgmake\rpgEditor\src-tauri\src\save\path_utils.rs D:\gpgmake\rpgEditor\src-tauri\src\save\plugin_parser.rs D:\gpgmake\rpgEditor\src-tauri\src\save\plugins_file.rs D:\gpgmake\rpgEditor\src-tauri\src\save\save.rs D:\gpgmake\rpgEditor\src-tauri\src\save\types.rs D:\gpgmake\rpgEditor\src-tauri\src\temp_plugins.rs D:\gpgmake\rpgEditor\src-tauri\src\utils.rs D:\gpgmake\rpgEditor\src-tauri\target\debug\build\rpgeditor-14bb3894b7ee3933\out\524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7 D:\gpgmake\rpgEditor\src-tauri\tauri.conf.json
